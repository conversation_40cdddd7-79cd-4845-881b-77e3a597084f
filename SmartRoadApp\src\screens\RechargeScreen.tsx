import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TextInput,
  Alert,
} from 'react-native';
import { useWallet } from '../context/WalletContext';

interface RechargeScreenProps {
  navigation: any;
}

const RechargeScreen: React.FC<RechargeScreenProps> = ({ navigation }) => {
  const [selectedMethod, setSelectedMethod] = useState('');
  const [amount, setAmount] = useState('');
  const [cardNumber, setCardNumber] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const { rechargeBalance } = useWallet();

  const rechargeMethods = [
    {
      id: 'visa',
      name: 'بطاقة فيزا/ماستركارد',
      icon: '💳',
      description: 'ادفع بأمان باستخدام بطاقتك البنكية',
    },
    {
      id: 'vodafone',
      name: 'فودافون كاش',
      icon: '📱',
      description: 'تجديد سريع عبر فودافون كاش',
    },
    {
      id: 'orange',
      name: 'أورانج موني',
      icon: '🟠',
      description: 'استخدم محفظة أورانج موني',
    },
    {
      id: 'etisalat',
      name: 'اتصالات كاش',
      icon: '💚',
      description: 'تجديد عبر اتصالات كاش',
    },
    {
      id: 'bank',
      name: 'تحويل بنكي',
      icon: '🏦',
      description: 'تحويل مباشر من حسابك البنكي',
    },
  ];

  const quickAmounts = [50, 100, 200, 500, 1000];

  const handleMethodSelect = (methodId: string) => {
    setSelectedMethod(methodId);
  };

  const handleQuickAmount = (value: number) => {
    setAmount(value.toString());
  };

  const handleRecharge = () => {
    if (!selectedMethod) {
      Alert.alert('خطأ', 'يرجى اختيار طريقة التجديد');
      return;
    }

    if (!amount || parseFloat(amount) <= 0) {
      Alert.alert('خطأ', 'يرجى إدخال مبلغ صحيح');
      return;
    }

    if (selectedMethod === 'visa' && !cardNumber) {
      Alert.alert('خطأ', 'يرجى إدخال رقم البطاقة');
      return;
    }

    if (['vodafone', 'orange', 'etisalat'].includes(selectedMethod) && !phoneNumber) {
      Alert.alert('خطأ', 'يرجى إدخال رقم الهاتف');
      return;
    }

    // تأكيد عملية التجديد
    Alert.alert(
      'تأكيد العملية',
      `هل تريد تجديد الرصيد بمبلغ ${amount} جنيه؟`,
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'تأكيد',
          onPress: async () => {
            try {
              const methodName = rechargeMethods.find(m => m.id === selectedMethod)?.name || selectedMethod;
              await rechargeBalance(parseFloat(amount), methodName);
              Alert.alert('نجح', 'تم تجديد الرصيد بنجاح!', [
                { text: 'موافق', onPress: () => navigation.goBack() },
              ]);
            } catch (error) {
              Alert.alert('خطأ', 'حدث خطأ أثناء تجديد الرصيد');
            }
          },
        },
      ]
    );
  };

  const renderMethodDetails = () => {
    if (selectedMethod === 'visa') {
      return (
        <View style={styles.detailsContainer}>
          <Text style={styles.detailsTitle}>بيانات البطاقة</Text>
          <TextInput
            style={styles.input}
            placeholder="رقم البطاقة"
            value={cardNumber}
            onChangeText={setCardNumber}
            keyboardType="numeric"
            maxLength={16}
          />
        </View>
      );
    }

    if (['vodafone', 'orange', 'etisalat'].includes(selectedMethod)) {
      return (
        <View style={styles.detailsContainer}>
          <Text style={styles.detailsTitle}>رقم الهاتف</Text>
          <TextInput
            style={styles.input}
            placeholder="01xxxxxxxxx"
            value={phoneNumber}
            onChangeText={setPhoneNumber}
            keyboardType="phone-pad"
            maxLength={11}
          />
        </View>
      );
    }

    return null;
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.backButton}>← رجوع</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>تجديد الرصيد</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content}>
        {/* Amount Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>المبلغ المطلوب</Text>
          <TextInput
            style={styles.amountInput}
            placeholder="أدخل المبلغ بالجنيه"
            value={amount}
            onChangeText={setAmount}
            keyboardType="numeric"
            textAlign="center"
          />

          <Text style={styles.quickAmountLabel}>مبالغ سريعة:</Text>
          <View style={styles.quickAmountsContainer}>
            {quickAmounts.map((value) => (
              <TouchableOpacity
                key={value}
                style={[
                  styles.quickAmountButton,
                  amount === value.toString() && styles.quickAmountButtonSelected,
                ]}
                onPress={() => handleQuickAmount(value)}
              >
                <Text
                  style={[
                    styles.quickAmountText,
                    amount === value.toString() && styles.quickAmountTextSelected,
                  ]}
                >
                  {value} جنيه
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Payment Methods */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>طريقة الدفع</Text>
          {rechargeMethods.map((method) => (
            <TouchableOpacity
              key={method.id}
              style={[
                styles.methodButton,
                selectedMethod === method.id && styles.methodButtonSelected,
              ]}
              onPress={() => handleMethodSelect(method.id)}
            >
              <View style={styles.methodContent}>
                <Text style={styles.methodIcon}>{method.icon}</Text>
                <View style={styles.methodInfo}>
                  <Text style={styles.methodName}>{method.name}</Text>
                  <Text style={styles.methodDescription}>{method.description}</Text>
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Method Details */}
        {renderMethodDetails()}

        {/* Recharge Button */}
        <TouchableOpacity style={styles.rechargeButton} onPress={handleRecharge}>
          <Text style={styles.rechargeButtonText}>تجديد الرصيد</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    fontSize: 16,
    color: '#3498db',
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  placeholder: {
    width: 50,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 15,
  },
  amountInput: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 20,
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    borderWidth: 2,
    borderColor: '#3498db',
    marginBottom: 15,
  },
  quickAmountLabel: {
    fontSize: 14,
    color: '#7f8c8d',
    marginBottom: 10,
  },
  quickAmountsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickAmountButton: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 10,
    marginBottom: 8,
    width: '30%',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  quickAmountButtonSelected: {
    backgroundColor: '#3498db',
    borderColor: '#3498db',
  },
  quickAmountText: {
    fontSize: 12,
    color: '#2c3e50',
    fontWeight: '600',
  },
  quickAmountTextSelected: {
    color: '#fff',
  },
  methodButton: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  methodButtonSelected: {
    borderColor: '#3498db',
    borderWidth: 2,
  },
  methodContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  methodIcon: {
    fontSize: 24,
    marginRight: 15,
  },
  methodInfo: {
    flex: 1,
  },
  methodName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
  },
  methodDescription: {
    fontSize: 12,
    color: '#7f8c8d',
    marginTop: 2,
  },
  detailsContainer: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 20,
    marginBottom: 20,
  },
  detailsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 10,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  rechargeButton: {
    backgroundColor: '#27ae60',
    borderRadius: 10,
    padding: 18,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 30,
  },
  rechargeButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default RechargeScreen;
