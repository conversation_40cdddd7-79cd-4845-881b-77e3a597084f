import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TextInput,
  Alert,
  Image,
  FlatList,
} from 'react-native';
import { useWallet } from '../context/WalletContext';
import { 
  getUserCars, 
  getCarById, 
  transferCarOwnership, 
  CarData, 
  getUserByNationalId,
  validateNationalId
} from '../utils/database';

interface TransferCarScreenProps {
  navigation: any;
}

const TransferCarScreen: React.FC<TransferCarScreenProps> = ({ navigation }) => {
  const { state } = useWallet();
  const [userCars, setUserCars] = useState<CarData[]>([]);
  const [selectedCar, setSelectedCar] = useState<CarData | null>(null);
  const [newOwnerNationalId, setNewOwnerNationalId] = useState('');
  const [newOwnerInfo, setNewOwnerInfo] = useState<{ name: string; phoneNumber: string } | null>(null);
  const [step, setStep] = useState(1); // 1: اختيار السيارة، 2: إدخال بيانات المالك الجديد، 3: تأكيد
  const [transferFee] = useState(150); // رسوم نقل الملكية

  useEffect(() => {
    if (state.user) {
      const cars = getUserCars(state.user.nationalId);
      setUserCars(cars);
    }
  }, [state.user]);

  const handleCarSelect = (car: CarData) => {
    setSelectedCar(car);
    setStep(2);
  };

  const handleCheckNewOwner = () => {
    if (!newOwnerNationalId || !validateNationalId(newOwnerNationalId)) {
      Alert.alert('خطأ', 'الرقم القومي غير صحيح أو غير موجود في النظام');
      return;
    }

    if (newOwnerNationalId === state.user?.nationalId) {
      Alert.alert('خطأ', 'لا يمكن نقل الملكية لنفس المالك');
      return;
    }

    const newOwner = getUserByNationalId(newOwnerNationalId);
    if (newOwner) {
      setNewOwnerInfo({
        name: newOwner.name,
        phoneNumber: newOwner.phoneNumber,
      });
      setStep(3);
    } else {
      Alert.alert('خطأ', 'المستخدم غير موجود في النظام');
    }
  };

  const handleTransferConfirm = () => {
    if (!selectedCar || !newOwnerNationalId || !state.user) {
      Alert.alert('خطأ', 'بيانات غير مكتملة');
      return;
    }

    if (state.balance < transferFee) {
      Alert.alert('خطأ', 'رصيد المحفظة غير كافي لدفع رسوم نقل الملكية');
      return;
    }

    Alert.alert(
      'تأكيد نقل الملكية',
      `هل أنت متأكد من نقل ملكية السيارة ${selectedCar.brand} ${selectedCar.model} إلى ${newOwnerInfo?.name}؟\n\nسيتم خصم ${transferFee} جنيه كرسوم نقل ملكية.`,
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'تأكيد',
          onPress: () => {
            // نقل الملكية
            const success = transferCarOwnership(selectedCar.id, newOwnerNationalId);
            if (success) {
              // تحديث قائمة السيارات
              const updatedCars = getUserCars(state.user!.nationalId);
              setUserCars(updatedCars);
              
              // إعادة تعيين الحالة
              setSelectedCar(null);
              setNewOwnerNationalId('');
              setNewOwnerInfo(null);
              setStep(1);
              
              Alert.alert(
                'تم بنجاح',
                'تم نقل ملكية السيارة بنجاح',
                [{ text: 'موافق', onPress: () => navigation.goBack() }]
              );
            } else {
              Alert.alert('خطأ', 'حدث خطأ أثناء نقل الملكية');
            }
          },
        },
      ]
    );
  };

  const renderCarItem = ({ item }: { item: CarData }) => (
    <TouchableOpacity
      style={[
        styles.carItem,
        selectedCar?.id === item.id && styles.selectedCarItem,
      ]}
      onPress={() => handleCarSelect(item)}
    >
      <View style={styles.carImageContainer}>
        <View style={[styles.carImage, { backgroundColor: item.color === 'أبيض' ? '#f5f5f5' : item.color === 'أسود' ? '#333' : '#3498db' }]}>
          <Text style={[styles.carImageText, { color: item.color === 'أسود' ? '#fff' : '#333' }]}>
            {item.brand.charAt(0)}
          </Text>
        </View>
      </View>
      <View style={styles.carInfo}>
        <Text style={styles.carBrandModel}>{item.brand} {item.model}</Text>
        <Text style={styles.carDetails}>موديل {item.year}</Text>
        <Text style={styles.carDetails}>لوحة: {item.plateNumber}</Text>
        <Text style={styles.carDetails}>اللون: {item.color}</Text>
      </View>
    </TouchableOpacity>
  );

  const renderStepOne = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>اختر السيارة المراد نقل ملكيتها</Text>
      {userCars.length > 0 ? (
        <FlatList
          data={userCars}
          renderItem={renderCarItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.carsList}
        />
      ) : (
        <View style={styles.noCarsContainer}>
          <Text style={styles.noCarsText}>لا توجد سيارات مسجلة باسمك</Text>
        </View>
      )}
    </View>
  );

  const renderStepTwo = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>أدخل بيانات المالك الجديد</Text>
      <View style={styles.selectedCarSummary}>
        <Text style={styles.selectedCarTitle}>السيارة المختارة:</Text>
        <Text style={styles.selectedCarDetails}>
          {selectedCar?.brand} {selectedCar?.model} - {selectedCar?.year}
        </Text>
        <Text style={styles.selectedCarDetails}>لوحة: {selectedCar?.plateNumber}</Text>
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>الرقم القومي للمالك الجديد</Text>
        <TextInput
          style={styles.input}
          value={newOwnerNationalId}
          onChangeText={setNewOwnerNationalId}
          placeholder="أدخل الرقم القومي (14 رقم)"
          keyboardType="numeric"
          maxLength={14}
        />
      </View>

      <TouchableOpacity
        style={styles.actionButton}
        onPress={handleCheckNewOwner}
      >
        <Text style={styles.actionButtonText}>التالي</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.backButton}
        onPress={() => {
          setStep(1);
          setSelectedCar(null);
        }}
      >
        <Text style={styles.backButtonText}>رجوع</Text>
      </TouchableOpacity>
    </View>
  );

  const renderStepThree = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>تأكيد نقل الملكية</Text>
      
      <View style={styles.confirmationCard}>
        <View style={styles.confirmationSection}>
          <Text style={styles.confirmationLabel}>بيانات السيارة:</Text>
          <Text style={styles.confirmationValue}>
            {selectedCar?.brand} {selectedCar?.model} - موديل {selectedCar?.year}
          </Text>
          <Text style={styles.confirmationValue}>لوحة: {selectedCar?.plateNumber}</Text>
          <Text style={styles.confirmationValue}>اللون: {selectedCar?.color}</Text>
        </View>
        
        <View style={styles.confirmationSection}>
          <Text style={styles.confirmationLabel}>المالك الحالي:</Text>
          <Text style={styles.confirmationValue}>{state.user?.name}</Text>
          <Text style={styles.confirmationValue}>الرقم القومي: {state.user?.nationalId}</Text>
        </View>
        
        <View style={styles.confirmationSection}>
          <Text style={styles.confirmationLabel}>المالك الجديد:</Text>
          <Text style={styles.confirmationValue}>{newOwnerInfo?.name}</Text>
          <Text style={styles.confirmationValue}>الرقم القومي: {newOwnerNationalId}</Text>
          <Text style={styles.confirmationValue}>رقم الهاتف: {newOwnerInfo?.phoneNumber}</Text>
        </View>
        
        <View style={styles.feeSection}>
          <Text style={styles.feeLabel}>رسوم نقل الملكية:</Text>
          <Text style={styles.feeValue}>{transferFee} جنيه</Text>
        </View>
      </View>

      <TouchableOpacity
        style={styles.confirmButton}
        onPress={handleTransferConfirm}
      >
        <Text style={styles.confirmButtonText}>تأكيد نقل الملكية</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.backButton}
        onPress={() => {
          setStep(2);
          setNewOwnerInfo(null);
        }}
      >
        <Text style={styles.backButtonText}>رجوع</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.backButtonText}>← رجوع</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>نقل ملكية سيارة</Text>
        <View style={{ width: 50 }} />
      </View>

      <View style={styles.stepsIndicator}>
        <View style={[styles.stepIndicator, step >= 1 && styles.activeStepIndicator]}>
          <Text style={[styles.stepIndicatorText, step >= 1 && styles.activeStepIndicatorText]}>1</Text>
        </View>
        <View style={styles.stepConnector} />
        <View style={[styles.stepIndicator, step >= 2 && styles.activeStepIndicator]}>
          <Text style={[styles.stepIndicatorText, step >= 2 && styles.activeStepIndicatorText]}>2</Text>
        </View>
        <View style={styles.stepConnector} />
        <View style={[styles.stepIndicator, step >= 3 && styles.activeStepIndicator]}>
          <Text style={[styles.stepIndicatorText, step >= 3 && styles.activeStepIndicatorText]}>3</Text>
        </View>
      </View>

      <ScrollView style={styles.content}>
        {step === 1 && renderStepOne()}
        {step === 2 && renderStepTwo()}
        {step === 3 && renderStepThree()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  backButtonText: {
    fontSize: 16,
    color: '#3498db',
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  stepsIndicator: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  stepIndicator: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#ecf0f1',
    justifyContent: 'center',
    alignItems: 'center',
  },
  activeStepIndicator: {
    backgroundColor: '#3498db',
  },
  stepIndicatorText: {
    color: '#7f8c8d',
    fontWeight: 'bold',
  },
  activeStepIndicatorText: {
    color: '#fff',
  },
  stepConnector: {
    width: 40,
    height: 2,
    backgroundColor: '#ecf0f1',
    marginHorizontal: 5,
  },
  stepContainer: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 20,
    textAlign: 'center',
  },
  carsList: {
    paddingBottom: 20,
  },
  carItem: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#eee',
  },
  selectedCarItem: {
    borderColor: '#3498db',
    borderWidth: 2,
  },
  carImageContainer: {
    marginRight: 15,
  },
  carImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#3498db',
    justifyContent: 'center',
    alignItems: 'center',
  },
  carImageText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  carInfo: {
    flex: 1,
  },
  carBrandModel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 5,
  },
  carDetails: {
    fontSize: 14,
    color: '#7f8c8d',
    marginBottom: 2,
  },
  noCarsContainer: {
    padding: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  noCarsText: {
    fontSize: 16,
    color: '#7f8c8d',
    textAlign: 'center',
  },
  selectedCarSummary: {
    backgroundColor: '#ecf0f1',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
  },
  selectedCarTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 5,
  },
  selectedCarDetails: {
    fontSize: 14,
    color: '#7f8c8d',
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 8,
    textAlign: 'right',
  },
  input: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#ddd',
    textAlign: 'right',
  },
  actionButton: {
    backgroundColor: '#3498db',
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
    marginBottom: 15,
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  backButton: {
    backgroundColor: '#ecf0f1',
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
  },
  confirmationCard: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#eee',
  },
  confirmationSection: {
    marginBottom: 15,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  confirmationLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 5,
  },
  confirmationValue: {
    fontSize: 14,
    color: '#7f8c8d',
    marginBottom: 2,
  },
  feeSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 10,
  },
  feeLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#e74c3c',
  },
  feeValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#e74c3c',
  },
  confirmButton: {
    backgroundColor: '#27ae60',
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
    marginBottom: 15,
  },
  confirmButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default TransferCarScreen;
