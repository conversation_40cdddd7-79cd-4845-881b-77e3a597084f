{
  "name": "@typescript-eslint/visitor-keys",
  "version": "7.18.0",
  "description": "Visitor keys used to help traverse the TypeScript-ESTree AST",
  "files": [
    "dist",
    "_ts4.3",
    "package.json",
    "README.md",
    "LICENSE"
  ],
  "type": "commonjs",
  "exports": {
    ".": {
      "types": "./dist/index.d.ts",
      "default": "./dist/index.js"
    },
    "./package.json": "./package.json"
  },
  "types": "./dist/index.d.ts",
  "engines": {
    "node": "^18.18.0 || >=20.0.0"
  },
  "repository": {
    "type": "git",
    "url": "https://github.com/typescript-eslint/typescript-eslint.git",
    "directory": "packages/visitor-keys"
  },
  "bugs": {
    "url": "https://github.com/typescript-eslint/typescript-eslint/issues"
  },
  "homepage": "https://typescript-eslint.io",
  "license": "MIT",
  "keywords": [
    "eslint",
    "typescript",
    "estree"
  ],
  "scripts": {
    "build": "tsc -b tsconfig.build.json",
    "postbuild": "downle