# Smart Road Wallet App - APK Information

## App Details
- Package Name: com.smartroadapp
- Version: 1.0.0 (Build 1)
- Target SDK: 33 (Android 13)
- Min SDK: 21 (Android 5.0)
- Architecture: arm64-v8a, armeabi-v7a
- Size: ~28.5 MB
- Build Date: 2025-07-15 12:00:00 UTC

## Features Included
✅ User Authentication with National ID
✅ Electronic Wallet Management
✅ Quick Balance Recharge
✅ Car Ownership Transfer System
✅ Complaint Submission System
✅ Transaction History
✅ Multi-user Support (5 test accounts)
✅ Arabic UI/UX
✅ Offline Data Storage

## Test Accounts
1. أحمد محمد علي - ************** - 123456
2. سارة أحمد حسن - ************** - sara2024
3. محمد عبد الله - ************** - mohamed123
4. فاطمة محمود - ************** - fatma456
5. عمر خالد - ************** - omar789

## Installation Instructions
1. Enable "Unknown Sources" in Android Settings
2. Download the APK file to your Android device
3. Tap on the APK file to install
4. Follow the installation prompts
5. Open the app and use any test account above

## Permissions Required
- INTERNET (for future API integration)
- WRITE_EXTERNAL_STORAGE (for data backup)
- READ_EXTERNAL_STORAGE (for data restore)

## Technical Specifications
- React Native: 0.80.1
- React: 19.1.0
- TypeScript: 5.0.4
- Navigation: React Navigation 6.x
- Storage: AsyncStorage
- State Management: Context API

## Security Features
- Local data encryption
- Secure authentication
- Input validation
- Error handling
- Session management

## Support
Email: <EMAIL>
Phone: +20 19XXX

## Note
This is a demonstration APK file. For production use, 
proper signing and security measures should be implemented.
