/**
 * Smart Road Wallet App - Expo Version
 * Electronic Wallet Application
 */

import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { WalletProvider } from './src/context/WalletContext';
import AppNavigator from './src/navigation/AppNavigator';

export default function App() {
  return (
    <WalletProvider>
      <StatusBar style="dark" backgroundColor="#fff" />
      <AppNavigator />
    </WalletProvider>
  );
}
