/**
 * تكوين API الرئيسي
 * يحتوي على الإعدادات الأساسية والدوال المساعدة للاتصال بالخادم
 */

// عنوان الخادم الأساسي
export const API_BASE_URL = 'https://api.smartroad.com/v1';

// الوقت المحدد للطلبات (بالمللي ثانية)
export const API_TIMEOUT = 10000;

// رؤوس الطلبات الافتراضية
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};

// أنواع الاستجابة
export enum ResponseStatus {
  SUCCESS = 'success',
  ERROR = 'error',
}

// واجهة الاستجابة العامة
export interface ApiResponse<T> {
  status: ResponseStatus;
  data?: T;
  message?: string;
  errors?: any;
  timestamp: string;
  requestId?: string;
}

// خطأ API المخصص
export class ApiError extends Error {
  status: number;
  errors?: any;
  requestId?: string;

  constructor(message: string, status: number, errors?: any, requestId?: string) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.errors = errors;
    this.requestId = requestId;
  }
}

/**
 * دالة مساعدة لإنشاء طلبات HTTP
 * @param endpoint نقطة النهاية للطلب
 * @param method طريقة الطلب (GET, POST, PUT, DELETE)
 * @param data البيانات المرسلة (اختياري)
 * @param headers رؤوس إضافية (اختياري)
 * @param withAuth إضافة رمز المصادقة (اختياري، افتراضي: true)
 * @returns وعد بالاستجابة
 */
export const fetchApi = async <T>(
  endpoint: string,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
  data?: any,
  headers: HeadersInit = {},
  withAuth: boolean = true
): Promise<ApiResponse<T>> => {
  try {
    // إنشاء عنوان URL كامل
    const url = `${API_BASE_URL}${endpoint}`;

    // إعداد رؤوس الطلب
    const requestHeaders: HeadersInit = {
      ...DEFAULT_HEADERS,
      ...headers,
    };

    // إضافة رمز المصادقة إذا كان مطلوباً
    if (withAuth) {
      const authToken = await getAuthToken();
      if (authToken) {
        requestHeaders['Authorization'] = `Bearer ${authToken}`;
      }
    }

    // إعداد خيارات الطلب
    const requestOptions: RequestInit = {
      method,
      headers: requestHeaders,
      body: data ? JSON.stringify(data) : undefined,
    };

    // إنشاء وعد بمهلة زمنية
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Request timeout')), API_TIMEOUT);
    });

    // إنشاء وعد بالطلب
    const fetchPromise = fetch(url, requestOptions);

    // انتظار الاستجابة مع مراعاة المهلة الزمنية
    const response = await Promise.race([fetchPromise, timeoutPromise]) as Response;

    // تحليل الاستجابة
    const responseData = await response.json();

    // التحقق من حالة الاستجابة
    if (!response.ok) {
      throw new ApiError(
        responseData.message || 'حدث خطأ أثناء الاتصال بالخادم',
        response.status,
        responseData.errors,
        responseData.requestId
      );
    }

    return responseData as ApiResponse<T>;
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }

    // تحويل الأخطاء العامة إلى أخطاء API
    throw new ApiError(
      error instanceof Error ? error.message : 'حدث خطأ غير معروف',
      500
    );
  }
};

/**
 * الحصول على رمز المصادقة من التخزين المحلي
 * @returns وعد برمز المصادقة
 */
export const getAuthToken = async (): Promise<string | null> => {
  try {
    // في التطبيق الحقيقي، سيتم استرداد الرمز من AsyncStorage
    // هذه محاكاة للعملية
    return 'mock_auth_token';
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
};

/**
 * دوال مساعدة للطلبات الشائعة
 */
export const apiGet = <T>(endpoint: string, headers?: HeadersInit, withAuth: boolean = true): Promise<ApiResponse<T>> => {
  return fetchApi<T>(endpoint, 'GET', undefined, headers, withAuth);
};

export const apiPost = <T>(endpoint: string, data: any, headers?: HeadersInit, withAuth: boolean = true): Promise<ApiResponse<T>> => {
  return fetchApi<T>(endpoint, 'POST', data, headers, withAuth);
};

export const apiPut = <T>(endpoint: string, data: any, headers?: HeadersInit, withAuth: boolean = true): Promise<ApiResponse<T>> => {
  return fetchApi<T>(endpoint, 'PUT', data, headers, withAuth);
};

export const apiDelete = <T>(endpoint: string, headers?: HeadersInit, withAuth: boolean = true): Promise<ApiResponse<T>> => {
  return fetchApi<T>(endpoint, 'DELETE', undefined, headers, withAuth);
};

export const apiPatch = <T>(endpoint: string, data: any, headers?: HeadersInit, withAuth: boolean = true): Promise<ApiResponse<T>> => {
  return fetchApi<T>(endpoint, 'PATCH', data, headers, withAuth);
};
