"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = loadConfig;
exports.loadConfigAsync = loadConfigAsync;
function _path() {
  const data = _interopRequireDefault(require("path"));
  _path = function () {
    return data;
  };
  return data;
}
function _cliTools() {
  const data = require("@react-native-community/cli-tools");
  _cliTools = function () {
    return data;
  };
  return data;
}
var _findDependencies = _interopRequireDefault(require("./findDependencies"));
var _resolveReactNativePath = _interopRequireDefault(require("./resolveReactNativePath"));
var _readConfigFromDisk = require("./readConfigFromDisk");
var _assign = _interopRequireDefault(require("./assign"));
var _merge = _interopRequireDefault(require("./merge"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function getDependencyConfig(root, dependencyName, finalConfig, config, userConfig) {
  return (0, _merge.default)({
    root,
    name: dependencyName,
    platforms: Object.keys(finalConfig.platforms).reduce((dependency, platform) => {
      var _config$dependency$pl;
      const platformConfig = finalConfig.platforms[platform];
      dependency[platform] =
      // Linking platforms is not supported
      Object.keys(config.platforms).length > 0 || !platformConfig ? null : platformConfig.dependencyConfig(root, (_config$dependency$pl = config.dependency.platforms) === null || _config$dependency$pl === void 0 ? void 0 : _config$dependency$pl[platform]);
      return dependency;
    }, {})
  }, userConfig.dependencies[dependencyName] || {});
}

// Try our best to figure out what version of React Native we're running. This is
// currently being used to get our deeplinks working, so it's only worried with
// the major and minor version.
function getReactNativeVersion(reactNativePath) {
  try {
    let semver = _cliTools().version.current(reactNativePath);
    if (