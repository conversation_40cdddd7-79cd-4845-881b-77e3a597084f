# واجهة برمجة التطبيقات (API) لتطبيق المحفظة الإلكترونية

## نظرة عامة

تم إضافة واجهة برمجة تطبيقات (API) كاملة لتطبيق المحفظة الإلكترونية، مما يتيح التفاعل مع الخدمات المختلفة مثل المصادقة، المحفظة، السيارات، والشكاوى. هذا الملف يشرح كيفية استخدام API في المشروع.

## هيكل الملفات

```
src/api/
├── apiConfig.ts         # التكوين الأساسي والدوال المساعدة
├── authService.ts       # خدمة المصادقة
├── walletService.ts     # خدمة المحفظة
├── carService.ts        # خدمة السيارات
├── complaintService.ts  # خدمة الشكاوى
└── index.ts             # ملف الفهرس الرئيسي
```

## الخدمات المتاحة

### 1. خدمة المصادقة (`authService.ts`)

تتعامل مع عمليات تسجيل الدخول والخروج وإدارة المستخدمين.

**الدوال الرئيسية:**
- `login(credentials)`: تسجيل الدخول
- `logout()`: تسجيل الخروج
- `refreshToken(refreshData)`: تجديد رمز المصادقة
- `getCurrentUser()`: الحصول على معلومات المستخدم الحالي
- `changePassword(passwordData)`: تغيير كلمة المرور
- `forgotPassword(forgotData)`: نسيان كلمة المرور
- `resetPassword(resetData)`: إعادة تعيين كلمة المرور

### 2. خدمة المحفظة (`walletService.ts`)

تتعامل مع عمليات المحفظة والمعاملات المالية.

**الدوال الرئيسية:**
- `getWalletBalance()`: الحصول على رصيد المحفظة
- `rechargeWallet(rechargeData)`: شحن المحفظة
- `transferMoney(transferData)`: تحويل الأموال
- `payBill(paymentData)`: دفع فاتورة
- `getTransactionHistory(page, limit, type, startDate, endDate)`: الحصول على سجل المعاملات
- `getTransactionDetails(transactionId)`: الحصول على تفاصيل معاملة محددة

### 3. خدمة السيارات (`carService.ts`)

تتعامل مع عمليات السيارات ونقل الملكية.

**الدوال الرئيسية:**
- `getUserCars()`: الحصول على قائمة السيارات المملوكة للمستخدم
- `getCarDetails(carId)`: الحصول على تفاصيل سيارة محددة
- `transferCarOwnership(transferData)`: نقل ملكية سيارة
- `validateNewOwner(nationalId)`: التحقق من صحة بيانات المالك الجديد
- `registerCar(carData)`: تسجيل سيارة جديدة
- `getTransferHistory(page, limit)`: الحصول على سجل نقل الملكية

### 4. خدمة الشكاوى (`complaintService.ts`)

تتعامل مع عمليات تقديم ومتابعة الشكاوى.

**الدوال الرئيسية:**
- `submitComplaint(complaintData)`: تقديم شكوى جديدة
- `getUserComplaints(page, limit, status, category)`: الحصول على قائمة الشكاوى الخاصة بالمستخدم
- `getComplaintDetails(complaintId)`: الحصول على تفاصيل شكوى محددة
- `getComplaintCategories()`: الحصول على فئات الشكاوى المتاحة
- `getComplaintStats()`: الحصول على إحصائيات الشكاوى للمستخدم
- `sendComplaintMessage(complaintId, message, attachments)`: إرسال رسالة في الشكوى

## كيفية استخدام API في المشروع

### 1. استيراد الخدمات المطلوبة

```typescript
// استيراد خدمات محددة
import { login, getWalletBalance } from '../api/authService';

// أو استيراد جميع الخدمات من ملف الفهرس
import { login, getWalletBalance, transferCarOwnership } from '../api';
```

### 2. استخدام الخدمات في الشاشات

#### مثال: شاشة تسجيل الدخول

```typescript
import React, { useState } from 'react';
import { View, TextInput, Button, Alert } from 'react-native';
import { login } from '../api';

const LoginScreen = ({ navigation }) => {
  const [nationalId, setNationalId] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const handleLogin = async () => {
    if (!nationalId || !password) {
      Alert.alert('خطأ', 'يرجى إدخال الرقم القومي وكلمة المرور');
      return;
    }

    setLoading(true);

    try {
      const response = await login({ nationalId, password });
      
      // حفظ بيانات المستخدم والرمز المميز
      // ...
      
      // الانتقال إلى الشاشة الرئيسية
      navigation.navigate('Wallet');
    } catch (error) {
      Alert.alert('خطأ', error.message || 'حدث خطأ أثناء تسجيل الدخول');
    } finally {
      setLoading(false);
    }
  };

  // ...
};
```

#### مثال: شاشة المحفظة

```typescript
import React, { useState, useEffect } from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { getWalletBalance, getTransactionHistory } from '../api';

const WalletScreen = () => {
  const [balance, setBalance] = useState(0);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchWalletData = async () => {
      try {
        // الحصول على رصيد المحفظة
        const balanceResponse = await getWalletBalance();
        setBalance(balanceResponse.data.balance);

        // الحصول على سجل المعاملات
        const transactionsResponse = await getTransactionHistory(1, 10);
        setTransactions(transactionsResponse.data.transactions);
      } catch (error) {
        console.error('Error fetching wallet data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchWalletData();
  }, []);

  // ...
};
```

#### مثال: شاشة نقل ملكية السيارة

```typescript
import React, { useState, useEffect } from 'react';
import { View, Text, FlatList, TouchableOpacity } from 'react-native';
import { getUserCars, transferCarOwnership, validateNewOwner } from '../api';

const TransferCarScreen = ({ navigation }) => {
  const [userCars, setUserCars] = useState([]);
  const [selectedCar, setSelectedCar] = useState(null);
  const [newOwnerNationalId, setNewOwnerNationalId] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchUserCars = async () => {
      try {
        const response = await getUserCars();
        setUserCars(response.data);
      } catch (error) {
        console.error('Error fetching user cars:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserCars();
  }, []);

  const handleTransferConfirm = async () => {
    try {
      // التحقق من صحة بيانات المالك الجديد
      const ownerResponse = await validateNewOwner(newOwnerNationalId);
      
      // نقل ملكية السيارة
      const transferResponse = await transferCarOwnership({
        carId: selectedCar.id,
        newOwnerNationalId,
        reason: 'بيع'
      });
      
      // عرض رسالة النجاح
      // ...
    } catch (error) {
      // عرض رسالة الخطأ
      // ...
    }
  };

  // ...
};
```

## التعامل مع الأخطاء

يوفر API آلية موحدة للتعامل مع الأخطاء:

```typescript
import { login, ApiError, handleApiError } from '../api';

try {
  const response = await login({
    nationalId: '29801152301234',
    password: 'wrong_password'
  });
} catch (error) {
  if (error instanceof ApiError) {
    // خطأ API محدد
    console.error(`خطأ: ${error.message}`);
    console.error(`رمز الحالة: ${error.status}`);
  } else {
    // خطأ عام
    console.error(`خطأ غير معروف: ${handleApiError(error)}`);
  }
}
```

## إعادة المحاولة والتعامل مع انقطاع الاتصال

يوفر API دوال مساعدة للتعامل مع انقطاع الاتصال وإعادة المحاولة:

```typescript
import { retryApiCall, checkNetworkConnection, getWalletBalance } from '../api';

// التحقق من حالة الاتصال
const isConnected = await checkNetworkConnection();

if (isConnected) {
  // إعادة المحاولة حتى 3 مرات مع تأخير متزايد
  try {
    const balanceResponse = await retryApiCall(
      () => getWalletBalance(),
      3,  // عدد المحاولات
      1000  // التأخير الأساسي (بالمللي ثانية)
    );
    
    console.log(`الرصيد: ${balanceResponse.data.balance}`);
  } catch (error) {
    console.error('فشلت جميع المحاولات:', error);
  }
}
```

## ملاحظات هامة

1. **المصادقة**: جميع الطلبات (باستثناء تسجيل الدخول وتجديد الرمز) تتطلب رمز مصادقة.
2. **التعامل مع الأخطاء**: استخدم دائماً بلوك `try/catch` للتعامل مع الأخطاء.
3. **الاتصال بالإنترنت**: تحقق من حالة الاتصال قبل إجراء طلبات مهمة.
4. **التوثيق الكامل**: للحصول على توثيق مفصل لجميع نقاط النهاية والواجهات، راجع ملف `API_DOCUMENTATION.md`.

## المزيد من المعلومات

للحصول على معلومات مفصلة حول جميع نقاط النهاية والواجهات والأمثلة، يرجى الرجوع إلى ملف التوثيق الكامل:

[توثيق API الكامل](./API_DOCUMENTATION.md)
