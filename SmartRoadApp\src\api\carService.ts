/**
 * خدمة السيارات
 * تحتوي على جميع العمليات المتعلقة بالسيارات ونقل الملكية
 */

import { apiGet, apiPost, apiPut, ApiResponse } from './apiConfig';

// واجهات البيانات
export interface Car {
  id: string;
  brand: string;
  model: string;
  year: number;
  plateNumber: string;
  color: string;
  ownerNationalId: string;
  ownerName: string;
  registrationDate: string;
  lastTransferDate?: string;
  status: 'نشط' | 'معلق' | 'محجوز';
}

export interface CarOwnershipTransfer {
  carId: string;
  currentOwnerNationalId: string;
  newOwnerNationalId: string;
  transferFee: number;
  reason?: string;
  documents?: string[];
}

export interface TransferRequest {
  carId: string;
  newOwnerNationalId: string;
  reason?: string;
  documents?: string[];
}

export interface TransferResponse {
  transferId: string;
  carId: string;
  previousOwner: string;
  newOwner: string;
  transferFee: number;
  status: 'مكتمل' | 'معلق' | 'مرفوض';
  transferDate: string;
  reference: string;
}

export interface CarRegistration {
  brand: string;
  model: string;
  year: number;
  plateNumber: string;
  color: string;
  engineNumber: string;
  chassisNumber: string;
  documents: string[];
}

export interface CarRegistrationResponse {
  carId: string;
  registrationNumber: string;
  status: string;
  registrationDate: string;
}

export interface TransferHistory {
  transfers: {
    id: string;
    carId: string;
    carDetails: string;
    previousOwner: string;
    newOwner: string;
    transferDate: string;
    status: string;
    transferFee: number;
  }[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
}

/**
 * الحصول على قائمة السيارات المملوكة للمستخدم
 * @returns وعد بالاستجابة
 */
export const getUserCars = async (): Promise<ApiResponse<Car[]>> => {
  return apiGet<Car[]>('/cars/my-cars');
};

/**
 * الحصول على تفاصيل سيارة محددة
 * @param carId معرف السيارة
 * @returns وعد بالاستجابة
 */
export const getCarDetails = async (carId: string): Promise<ApiResponse<Car>> => {
  return apiGet<Car>(`/cars/${carId}`);
};

/**
 * نقل ملكية سيارة
 * @param transferData بيانات نقل الملكية
 * @returns وعد بالاستجابة
 */
export const transferCarOwnership = async (transferData: TransferRequest): Promise<ApiResponse<TransferResponse>> => {
  return apiPost<TransferResponse>('/cars/transfer', transferData);
};

/**
 * التحقق من صحة بيانات المالك الجديد
 * @param nationalId الرقم القومي للمالك الجديد
 * @returns وعد بالاستجابة
 */
export const validateNewOwner = async (nationalId: string): Promise<ApiResponse<{ name: string; phoneNumber: string; email: string }>> => {
  return apiPost<{ name: string; phoneNumber: string; email: string }>('/cars/validate-owner', { nationalId });
};

/**
 * تسجيل سيارة جديدة
 * @param carData بيانات السيارة الجديدة
 * @returns وعد بالاستجابة
 */
export const registerCar = async (carData: CarRegistration): Promise<ApiResponse<CarRegistrationResponse>> => {
  return apiPost<CarRegistrationResponse>('/cars/register', carData);
};

/**
 * تحديث بيانات السيارة
 * @param carId معرف السيارة
 * @param updateData البيانات المحدثة
 * @returns وعد بالاستجابة
 */
export const updateCarDetails = async (carId: string, updateData: Partial<Car>): Promise<ApiResponse<Car>> => {
  return apiPut<Car>(`/cars/${carId}`, updateData);
};

/**
 * الحصول على سجل نقل الملكية
 * @param page رقم الصفحة (اختياري، افتراضي: 1)
 * @param limit عدد العناصر في الصفحة (اختياري، افتراضي: 20)
 * @returns وعد بالاستجابة
 */
export const getTransferHistory = async (
  page: number = 1,
  limit: number = 20
): Promise<ApiResponse<TransferHistory>> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  });

  return apiGet<TransferHistory>(`/cars/transfer-history?${params.toString()}`);
};

/**
 * الحصول على تفاصيل عملية نقل ملكية محددة
 * @param transferId معرف عملية النقل
 * @returns وعد بالاستجابة
 */
export const getTransferDetails = async (transferId: string): Promise<ApiResponse<TransferResponse>> => {
  return apiGet<TransferResponse>(`/cars/transfers/${transferId}`);
};

/**
 * البحث عن السيارات
 * @param query نص البحث
 * @param filters فلاتر البحث (اختياري)
 * @returns وعد بالاستجابة
 */
export const searchCars = async (
  query: string,
  filters?: {
    brand?: string;
    model?: string;
    yearFrom?: number;
    yearTo?: number;
    color?: string;
  }
): Promise<ApiResponse<Car[]>> => {
  const params = new URLSearchParams({ query });
  
  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });
  }

  return apiGet<Car[]>(`/cars/search?${params.toString()}`);
};

/**
 * الحصول على رسوم نقل الملكية
 * @param carId معرف السيارة
 * @returns وعد بالاستجابة
 */
export const getTransferFees = async (carId: string): Promise<ApiResponse<{ baseFee: number; additionalFees: any[]; totalFee: number }>> => {
  return apiGet<{ baseFee: number; additionalFees: any[]; totalFee: number }>(`/cars/${carId}/transfer-fees`);
};

// نقاط النهاية للـ API
export const CAR_ENDPOINTS = {
  MY_CARS: '/cars/my-cars',
  CAR_DETAILS: '/cars/:carId',
  TRANSFER: '/cars/transfer',
  VALIDATE_OWNER: '/cars/validate-owner',
  REGISTER: '/cars/register',
  UPDATE: '/cars/:carId',
  TRANSFER_HISTORY: '/cars/transfer-history',
  TRANSFER_DETAILS: '/cars/transfers/:transferId',
  SEARCH: '/cars/search',
  TRANSFER_FEES: '/cars/:carId/transfer-fees',
} as const;
