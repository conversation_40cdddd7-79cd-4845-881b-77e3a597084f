import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useWallet } from '../context/WalletContext';
import { validateNationalId, getUserByNationalId } from '../utils/database';

interface LoginScreenProps {
  navigation: any;
}

const LoginScreen: React.FC<LoginScreenProps> = ({ navigation }) => {
  const [nationalId, setNationalId] = useState('');
  const [password, setPassword] = useState('');
  const { state, login } = useWallet();

  const checkNationalId = (id: string): boolean => {
    // التحقق من أن الرقم القومي 14 رقم وموجود في قاعدة البيانات
    return validateNationalId(id);
  };

  const handleLogin = async () => {
    if (!nationalId || !password) {
      Alert.alert('خطأ', 'يرجى إدخال الرقم القومي وكلمة المرور');
      return;
    }

    if (!checkNationalId(nationalId)) {
      Alert.alert('خطأ', 'الرقم القومي غير صحيح أو غير موجود في النظام');
      return;
    }

    if (password.length < 6) {
      Alert.alert('خطأ', 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
      return;
    }

    const success = await login(nationalId, password);

    if (success) {
      navigation.navigate('Wallet');
    } else if (state.error) {
      Alert.alert('خطأ', state.error);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>المحفظة الإلكترونية</Text>
            <Text style={styles.subtitle}>تسجيل الدخول</Text>
          </View>

          <View style={styles.form}>
            <View style={styles.inputContainer}>
              <Text style={styles.label}>الرقم القومي</Text>
              <TextInput
                style={styles.input}
                value={nationalId}
                onChangeText={setNationalId}
                placeholder="أدخل الرقم القومي (14 رقم)"
                keyboardType="numeric"
                maxLength={14}
                textAlign="right"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>كلمة المرور</Text>
              <TextInput
                style={styles.input}
                value={password}
                onChangeText={setPassword}
                placeholder="أدخل كلمة المرور"
                secureTextEntry
                textAlign="right"
              />
            </View>

            <TouchableOpacity
              style={[styles.loginButton, state.loading && styles.loginButtonDisabled]}
              onPress={handleLogin}
              disabled={state.loading}
            >
              <Text style={styles.loginButtonText}>
                {state.loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.footer}>
            <Text style={styles.demoText}>
              حسابات للتجربة:{'\n'}
              أحمد محمد: 29801152301234 - 123456{'\n'}
              سارة أحمد: 29505201234567 - sara2024{'\n'}
              محمد عبد الله: 29203151987654 - mohamed123{'\n'}
              فاطمة محمود: 29712082345678 - fatma456{'\n'}
              عمر خالد: 29409301876543 - omar789
            </Text>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 18,
    color: '#7f8c8d',
    textAlign: 'center',
  },
  form: {
    marginBottom: 30,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 8,
    textAlign: 'right',
  },
  input: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#ddd',
    textAlign: 'right',
  },
  loginButton: {
    backgroundColor: '#3498db',
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
    marginTop: 20,
  },
  loginButtonDisabled: {
    backgroundColor: '#bdc3c7',
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  footer: {
    alignItems: 'center',
  },
  demoText: {
    fontSize: 14,
    color: '#7f8c8d',
    textAlign: 'center',
    backgroundColor: '#ecf0f1',
    padding: 15,
    borderRadius: 10,
  },
});

export default LoginScreen;
