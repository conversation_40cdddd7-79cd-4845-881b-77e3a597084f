# ميزة تقديم الشكاوى 📝

## الوصف
تم إضافة نظام شامل لتقديم الشكاوى يتيح للمستخدمين تقديم شكاواهم ومتابعتها بطريقة منظمة ومهنية.

## المميزات الجديدة

### 1. زر تقديم الشكوى 📞
- زر جديد في قسم "العمليات السريعة" في الشاشة الرئيسية
- ينقل المستخدم إلى شاشة تقديم الشكوى المتخصصة

### 2. شاشة تقديم الشكوى الشاملة

#### أ. معلومات مقدم الشكوى
- عرض تلقائي لبيانات المستخدم (الاسم والرقم القومي)
- ضمان ربط الشكوى بالمستخدم الصحيح

#### ب. أنواع الشكاوى (6 فئات)
1. **مشكلة تقنية** ⚙️ - مشاكل في التطبيق أو النظام
2. **مشكلة في الدفع** 💳 - مشاكل الشحن أو المعاملات المالية
3. **مشكلة نقل ملكية** 🚗 - مشاكل في نقل ملكية السيارات
4. **مشكلة في الحساب** 👤 - مشاكل تسجيل الدخول أو البيانات الشخصية
5. **جودة الخدمة** 📞 - شكاوى حول مستوى الخدمة المقدمة
6. **أخرى** 📝 - أي مشاكل أخرى غير مصنفة

#### ج. تفاصيل الشكوى
- **عنوان الشكوى**: عنوان مختصر وواضح (حد أقصى 100 حرف)
- **تفاصيل الشكوى**: وصف مفصل للمشكلة (حد أقصى 500 حرف)
- عداد أحرف تفاعلي لمساعدة المستخدم

#### د. مستوى الأولوية (4 مستويات)
1. **منخفض** - مشاكل غير عاجلة
2. **متوسط** - مشاكل عادية (افتراضي)
3. **عالي** - مشاكل مهمة تحتاج اهتمام سريع
4. **عاجل** - مشاكل حرجة تحتاج تدخل فوري

#### هـ. طريقة التواصل المفضلة (3 خيارات)
1. **هاتف** - التواصل عبر المكالمات (افتراضي)
2. **بريد إلكتروني** - التواصل عبر الإيميل
3. **رسائل نصية** - التواصل عبر الرسائل النصية

### 3. نظام رقم الشكوى
- إنشاء رقم شكوى فريد تلقائياً
- تنسيق: `CMP` + 6 أرقام من الوقت + 3 أرقام عشوائية
- مثال: `CMP123456789`

### 4. رسالة تأكيد النجاح
- نافذة منبثقة تؤكد إرسال الشكوى
- عرض رقم الشكوى للمتابعة
- معلومات عن مدة الرد المتوقعة (24-48 ساعة)

## كيفية الاستخدام

### خطوات تقديم الشكوى:
1. **سجل الدخول** بحسابك
2. في الشاشة الرئيسية، اضغط على **"تقديم شكوى"**
3. **اختر نوع الشكوى** من الفئات المتاحة
4. **أدخل عنوان الشكوى** (مختصر وواضح)
5. **اكتب تفاصيل الشكوى** (وصف مفصل للمشكلة)
6. **حدد مستوى الأولوية** (منخفض، متوسط، عالي، عاجل)
7. **اختر طريقة التواصل المفضلة**
8. **اضغط "إرسال الشكوى"**
9. **احفظ رقم الشكوى** للمتابعة

### مثال عملي:
- **المستخدم**: أحمد محمد علي
- **نوع الشكوى**: مشكلة في الدفع 💳
- **العنوان**: "فشل في شحن المحفظة رغم خصم المبلغ"
- **التفاصيل**: "قمت بشحن المحفظة بمبلغ 200 جنيه عبر فيزا، تم خصم المبلغ من البطاقة لكن لم يتم إضافته للمحفظة"
- **الأولوية**: عالي
- **التواصل**: هاتف
- **رقم الشكوى**: CMP654321987

## التحقق من صحة البيانات

### التحققات المطلوبة:
- ✅ اختيار نوع الشكوى إجباري
- ✅ عنوان الشكوى إجباري (لا يمكن أن يكون فارغاً)
- ✅ تفاصيل الشكوى إجبارية (10 أحرف على الأقل)
- ✅ حد أقصى 100 حرف للعنوان
- ✅ حد أقصى 500 حرف للتفاصيل

### رسائل الخطأ:
- "يرجى اختيار نوع الشكوى"
- "يرجى إدخال عنوان الشكوى"
- "يرجى إدخال تفاصيل الشكوى"
- "يرجى إدخال تفاصيل أكثر للشكوى (10 أحرف على الأقل)"

## التصميم والواجهة

### العناصر البصرية:
- 🎨 **تصميم متجاوب**: يتكيف مع جميع أحجام الشاشات
- 🌈 **ألوان متناسقة**: نظام ألوان موحد مع باقي التطبيق
- 📱 **أيقونات تعبيرية**: لكل فئة شكوى أيقونة مميزة
- ✨ **تأثيرات بصرية**: للعناصر المختارة والأزرار

### تجربة المستخدم:
- 📝 **نموذج منظم**: خطوات واضحة ومنطقية
- 🔢 **عداد أحرف**: لمساعدة المستخدم في كتابة التفاصيل
- ⚡ **استجابة سريعة**: تفاعل فوري مع إدخالات المستخدم
- 💡 **إرشادات واضحة**: نصائح ومعلومات مفيدة

## الملفات المحدثة

### الملفات الجديدة:
- `src/screens/ComplaintScreen.tsx`: شاشة تقديم الشكاوى الكاملة

### الملفات المحدثة:
- `src/screens/WalletScreen.tsx`: إضافة زر تقديم الشكوى
- `src/navigation/AppNavigator.tsx`: إضافة التنقل لشاشة الشكاوى

## الفوائد

### للمستخدمين:
- ✅ **سهولة تقديم الشكاوى**: واجهة بسيطة ومفهومة
- ✅ **تصنيف دقيق**: فئات واضحة لأنواع المشاكل
- ✅ **متابعة منظمة**: رقم شكوى لكل حالة
- ✅ **مرونة في التواصل**: خيارات متعددة للرد

### للإدارة:
- ✅ **تصنيف تلقائي**: للشكاوى حسب النوع والأولوية
- ✅ **معلومات كاملة**: بيانات المستخدم والمشكلة
- ✅ **أولويات واضحة**: لترتيب معالجة الشكاوى
- ✅ **طرق تواصل محددة**: حسب تفضيل المستخدم

## ملاحظات مهمة
- 📞 **مدة الرد**: 24-48 ساعة عمل
- 🔢 **رقم الشكوى**: يجب حفظه للمتابعة
- 📝 **التفاصيل**: كلما كانت أكثر دقة، كان الحل أسرع
- ⏰ **الأولوية**: تؤثر على ترتيب معالجة الشكوى

**الآن يمكن للمستخدمين تقديم شكاواهم بطريقة منظمة ومهنية! 📝✨**
