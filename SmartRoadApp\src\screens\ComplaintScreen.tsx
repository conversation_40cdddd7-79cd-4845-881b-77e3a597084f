import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TextInput,
  Alert,
  Modal,
} from 'react-native';
import { useWallet } from '../context/WalletContext';

interface ComplaintScreenProps {
  navigation: any;
}

const ComplaintScreen: React.FC<ComplaintScreenProps> = ({ navigation }) => {
  const { state } = useWallet();
  const [selectedCategory, setSelectedCategory] = useState('');
  const [complaintTitle, setComplaintTitle] = useState('');
  const [complaintDescription, setComplaintDescription] = useState('');
  const [priority, setPriority] = useState('متوسط');
  const [contactMethod, setContactMethod] = useState('هاتف');
  const [successModalVisible, setSuccessModalVisible] = useState(false);
  const [complaintNumber, setComplaintNumber] = useState('');

  const complaintCategories = [
    { id: 'technical', name: 'مشكلة تقنية', icon: '⚙️' },
    { id: 'payment', name: 'مشكلة في الدفع', icon: '💳' },
    { id: 'transfer', name: 'مشكلة نقل ملكية', icon: '🚗' },
    { id: 'account', name: 'مشكلة في الحساب', icon: '👤' },
    { id: 'service', name: 'جودة الخدمة', icon: '📞' },
    { id: 'other', name: 'أخرى', icon: '📝' },
  ];

  const priorityLevels = ['منخفض', 'متوسط', 'عالي', 'عاجل'];
  const contactMethods = ['هاتف', 'بريد إلكتروني', 'رسائل نصية'];

  const generateComplaintNumber = () => {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `CMP${timestamp}${random}`;
  };

  const handleSubmitComplaint = () => {
    if (!selectedCategory) {
      Alert.alert('خطأ', 'يرجى اختيار نوع الشكوى');
      return;
    }

    if (!complaintTitle.trim()) {
      Alert.alert('خطأ', 'يرجى إدخال عنوان الشكوى');
      return;
    }

    if (!complaintDescription.trim()) {
      Alert.alert('خطأ', 'يرجى إدخال تفاصيل الشكوى');
      return;
    }

    if (complaintDescription.trim().length < 10) {
      Alert.alert('خطأ', 'يرجى إدخال تفاصيل أكثر للشكوى (10 أحرف على الأقل)');
      return;
    }

    // إنشاء رقم الشكوى
    const newComplaintNumber = generateComplaintNumber();
    setComplaintNumber(newComplaintNumber);

    // محاكاة إرسال الشكوى
    setTimeout(() => {
      setSuccessModalVisible(true);
    }, 1000);
  };

  const handleComplaintSuccess = () => {
    setSuccessModalVisible(false);
    // إعادة تعيين النموذج
    setSelectedCategory('');
    setComplaintTitle('');
    setComplaintDescription('');
    setPriority('متوسط');
    setContactMethod('هاتف');
    navigation.goBack();
  };

  const renderCategoryItem = (category: any) => (
    <TouchableOpacity
      key={category.id}
      style={[
        styles.categoryItem,
        selectedCategory === category.id && styles.selectedCategoryItem,
      ]}
      onPress={() => setSelectedCategory(category.id)}
    >
      <Text style={styles.categoryIcon}>{category.icon}</Text>
      <Text
        style={[
          styles.categoryText,
          selectedCategory === category.id && styles.selectedCategoryText,
        ]}
      >
        {category.name}
      </Text>
    </TouchableOpacity>
  );

  const renderPriorityItem = (level: string) => (
    <TouchableOpacity
      key={level}
      style={[
        styles.priorityItem,
        priority === level && styles.selectedPriorityItem,
      ]}
      onPress={() => setPriority(level)}
    >
      <Text
        style={[
          styles.priorityText,
          priority === level && styles.selectedPriorityText,
        ]}
      >
        {level}
      </Text>
    </TouchableOpacity>
  );

  const renderContactMethodItem = (method: string) => (
    <TouchableOpacity
      key={method}
      style={[
        styles.contactMethodItem,
        contactMethod === method && styles.selectedContactMethodItem,
      ]}
      onPress={() => setContactMethod(method)}
    >
      <Text
        style={[
          styles.contactMethodText,
          contactMethod === method && styles.selectedContactMethodText,
        ]}
      >
        {method}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.backButton}>← رجوع</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>تقديم شكوى</Text>
        <View style={{ width: 50 }} />
      </View>

      <ScrollView style={styles.content}>
        {/* معلومات المستخدم */}
        <View style={styles.userInfoCard}>
          <Text style={styles.userInfoTitle}>بيانات مقدم الشكوى</Text>
          <Text style={styles.userInfoText}>الاسم: {state.user?.name}</Text>
          <Text style={styles.userInfoText}>الرقم القومي: {state.user?.nationalId}</Text>
        </View>

        {/* نوع الشكوى */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>نوع الشكوى *</Text>
          <View style={styles.categoriesGrid}>
            {complaintCategories.map(renderCategoryItem)}
          </View>
        </View>

        {/* عنوان الشكوى */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>عنوان الشكوى *</Text>
          <TextInput
            style={styles.titleInput}
            value={complaintTitle}
            onChangeText={setComplaintTitle}
            placeholder="أدخل عنوان مختصر للشكوى"
            maxLength={100}
          />
        </View>

        {/* تفاصيل الشكوى */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>تفاصيل الشكوى *</Text>
          <TextInput
            style={styles.descriptionInput}
            value={complaintDescription}
            onChangeText={setComplaintDescription}
            placeholder="اشرح المشكلة بالتفصيل..."
            multiline
            numberOfLines={6}
            textAlignVertical="top"
            maxLength={500}
          />
          <Text style={styles.characterCount}>
            {complaintDescription.length}/500 حرف
          </Text>
        </View>

        {/* أولوية الشكوى */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>أولوية الشكوى</Text>
          <View style={styles.priorityGrid}>
            {priorityLevels.map(renderPriorityItem)}
          </View>
        </View>

        {/* طريقة التواصل المفضلة */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>طريقة التواصل المفضلة</Text>
          <View style={styles.contactMethodGrid}>
            {contactMethods.map(renderContactMethodItem)}
          </View>
        </View>

        {/* زر الإرسال */}
        <TouchableOpacity
          style={styles.submitButton}
          onPress={handleSubmitComplaint}
        >
          <Text style={styles.submitButtonText}>إرسال الشكوى</Text>
        </TouchableOpacity>

        <View style={styles.noteContainer}>
          <Text style={styles.noteText}>
            📝 ملاحظة: سيتم الرد على شكواك خلال 24-48 ساعة عمل
          </Text>
        </View>
      </ScrollView>

      {/* Modal نجاح الإرسال */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={successModalVisible}
        onRequestClose={() => setSuccessModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.successModal}>
            <Text style={styles.successIcon}>✅</Text>
            <Text style={styles.successTitle}>تم إرسال الشكوى بنجاح</Text>
            <Text style={styles.successMessage}>
              رقم الشكوى: {complaintNumber}
            </Text>
            <Text style={styles.successNote}>
              سيتم التواصل معك خلال 24-48 ساعة عمل
            </Text>
            <TouchableOpacity
              style={styles.successButton}
              onPress={handleComplaintSuccess}
            >
              <Text style={styles.successButtonText}>موافق</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    fontSize: 16,
    color: '#3498db',
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  userInfoCard: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#eee',
  },
  userInfoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 10,
  },
  userInfoText: {
    fontSize: 14,
    color: '#7f8c8d',
    marginBottom: 5,
  },
  section: {
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 15,
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  categoryItem: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    width: '48%',
    alignItems: 'center',
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#eee',
  },
  selectedCategoryItem: {
    borderColor: '#3498db',
    backgroundColor: '#e3f2fd',
  },
  categoryIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  categoryText: {
    fontSize: 14,
    color: '#2c3e50',
    textAlign: 'center',
  },
  selectedCategoryText: {
    color: '#3498db',
    fontWeight: 'bold',
  },
  titleInput: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#ddd',
    textAlign: 'right',
  },
  descriptionInput: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#ddd',
    textAlign: 'right',
    minHeight: 120,
  },
  characterCount: {
    fontSize: 12,
    color: '#7f8c8d',
    textAlign: 'left',
    marginTop: 5,
  },
  priorityGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  priorityItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 10,
    flex: 1,
    marginHorizontal: 2,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  selectedPriorityItem: {
    backgroundColor: '#e74c3c',
    borderColor: '#e74c3c',
  },
  priorityText: {
    fontSize: 14,
    color: '#2c3e50',
  },
  selectedPriorityText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  contactMethodGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  contactMethodItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 10,
    flex: 1,
    marginHorizontal: 2,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  selectedContactMethodItem: {
    backgroundColor: '#2ecc71',
    borderColor: '#2ecc71',
  },
  contactMethodText: {
    fontSize: 14,
    color: '#2c3e50',
  },
  selectedContactMethodText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  submitButton: {
    backgroundColor: '#e74c3c',
    borderRadius: 10,
    padding: 18,
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 20,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  noteContainer: {
    backgroundColor: '#fff3cd',
    borderRadius: 10,
    padding: 15,
    marginBottom: 30,
    borderWidth: 1,
    borderColor: '#ffeaa7',
  },
  noteText: {
    fontSize: 14,
    color: '#856404',
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  successModal: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 30,
    alignItems: 'center',
    width: '90%',
  },
  successIcon: {
    fontSize: 48,
    marginBottom: 15,
  },
  successTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 10,
    textAlign: 'center',
  },
  successMessage: {
    fontSize: 16,
    color: '#27ae60',
    marginBottom: 10,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  successNote: {
    fontSize: 14,
    color: '#7f8c8d',
    marginBottom: 20,
    textAlign: 'center',
  },
  successButton: {
    backgroundColor: '#27ae60',
    borderRadius: 10,
    paddingHorizontal: 30,
    paddingVertical: 12,
  },
  successButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default ComplaintScreen;
