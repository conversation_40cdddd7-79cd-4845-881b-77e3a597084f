#!/bin/bash

# <PERSON>ript to build APK for SmartRoad App

echo "===== Building APK for SmartRoad App ====="

# Navigate to the project directory
cd "$(dirname "$0")/../.."

# Check if keystore exists, if not create it
if [ ! -f "./android/app/smartroad-release-key.keystore" ]; then
    echo "===== Creating release keystore ====="
    keytool -genkeypair -v -storetype PKCS12 -keystore ./android/app/smartroad-release-key.keystore -alias smartroad-key -keyalg RSA -keysize 2048 -validity 10000 -storepass smartroad123 -keypass smartroad123 -dname "CN=Smart Road,OU=Mobile,O=Smart Road Inc,L=Cairo,ST=Cairo,C=EG"
    echo "===== Keystore created successfully ====="
fi

# Clean the project
echo "===== Cleaning project ====="
cd android
./gradlew clean
cd ..

# Install dependencies
echo "===== Installing dependencies ====="
npm install

# Build the APK
echo "===== Building release APK ====="
cd android
./gradlew assembleRelease

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "===== APK built successfully ====="
    echo "APK location: $(pwd)/app/build/outputs/apk/release/app-release.apk"
    
    # Create a directory for the APK in the project root
    mkdir -p ../apk
    
    # Copy the APK to the project root
    cp app/build/outputs/apk/release/app-release.apk ../apk/SmartRoadApp.apk
    
    echo "APK copied to: $(pwd)/../apk/SmartRoadApp.apk"
else
    echo "===== APK build failed ====="
fi

cd ..
echo "===== Build process completed ====="
