import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
  Modal,
  TextInput,
} from 'react-native';
import { useWallet } from '../context/WalletContext';

interface WalletScreenProps {
  navigation: any;
}

const WalletScreen: React.FC<WalletScreenProps> = ({ navigation }) => {
  const { state, logout, rechargeBalance } = useWallet();
  const [rechargeModalVisible, setRechargeModalVisible] = useState(false);
  const [rechargeAmount, setRechargeAmount] = useState('');
  const [selectedMethod, setSelectedMethod] = useState('فيزا');

  const handleRecharge = () => {
    navigation.navigate('Recharge');
  };

  const handleQuickRecharge = () => {
    setRechargeModalVisible(true);
  };

  const handleRechargeSubmit = async () => {
    if (!rechargeAmount || parseFloat(rechargeAmount) <= 0) {
      Alert.alert('خطأ', 'يرجى إدخال مبلغ صحيح');
      return;
    }

    const amount = parseFloat(rechargeAmount);

    Alert.alert(
      'تأكيد الشحن',
      `هل تريد شحن المحفظة بمبلغ ${amount} جنيه عبر ${selectedMethod}؟`,
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'تأكيد',
          onPress: async () => {
            try {
              await rechargeBalance(amount, selectedMethod);
              setRechargeModalVisible(false);
              setRechargeAmount('');
              Alert.alert('نجح', `تم شحن المحفظة بمبلغ ${amount} جنيه بنجاح!`);
            } catch (error) {
              Alert.alert('خطأ', 'حدث خطأ أثناء شحن المحفظة');
            }
          },
        },
      ]
    );
  };

  const quickAmounts = [50, 100, 200, 500];
  const rechargeMethods = ['فيزا', 'فودافون كاش', 'أورانج موني', 'اتصالات كاش'];

  const handleLogout = () => {
    Alert.alert(
      'تسجيل الخروج',
      'هل أنت متأكد من تسجيل الخروج؟',
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'تسجيل الخروج',
          onPress: async () => {
            await logout();
            navigation.navigate('Login');
          }
        },
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.userInfo}>
            <Text style={styles.welcomeText}>مرحباً</Text>
            <Text style={styles.userName}>{state.user?.name}</Text>
            <Text style={styles.nationalId}>الرقم القومي: {state.user?.nationalId}</Text>
          </View>
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Text style={styles.logoutText}>خروج</Text>
          </TouchableOpacity>
        </View>

        {/* Balance Card */}
        <View style={styles.balanceCard}>
          <Text style={styles.balanceLabel}>رصيد المحفظة</Text>
          <Text style={styles.balanceAmount}>{state.balance.toFixed(2)} جنيه</Text>
          <View style={styles.rechargeButtonsContainer}>
            <TouchableOpacity style={styles.rechargeButton} onPress={handleRecharge}>
              <Text style={styles.rechargeButtonText}>تجديد الرصيد</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.quickRechargeButton} onPress={handleQuickRecharge}>
              <Text style={styles.quickRechargeButtonText}>شحن سريع</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.actionsContainer}>
          <Text style={styles.sectionTitle}>العمليات السريعة</Text>
          <View style={styles.actionsGrid}>
            <TouchableOpacity style={styles.actionButton}>
              <Text style={styles.actionText}>دفع فاتورة</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Text style={styles.actionText}>تحويل أموال</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Text style={styles.actionText}>شحن رصيد</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Text style={styles.actionText}>كشف حساب</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Recent Transactions */}
        <View style={styles.transactionsContainer}>
          <Text style={styles.sectionTitle}>آخر العمليات</Text>
          {state.transactions.slice(0, 5).map((transaction) => (
            <View key={transaction.id} style={styles.transactionItem}>
              <View style={styles.transactionInfo}>
                <Text style={styles.transactionType}>{transaction.type}</Text>
                <Text style={styles.transactionDate}>{transaction.date}</Text>
                <Text style={styles.transactionMethod}>{transaction.method}</Text>
              </View>
              <Text
                style={[
                  styles.transactionAmount,
                  transaction.amount > 0 ? styles.positiveAmount : styles.negativeAmount,
                ]}
              >
                {transaction.amount > 0 ? '+' : ''}{transaction.amount} جنيه
              </Text>
            </View>
          ))}
        </View>
      </ScrollView>

      {/* Modal للشحن السريع */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={rechargeModalVisible}
        onRequestClose={() => setRechargeModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>شحن المحفظة</Text>

            {/* إدخال المبلغ */}
            <View style={styles.amountSection}>
              <Text style={styles.inputLabel}>المبلغ (جنيه)</Text>
              <TextInput
                style={styles.amountInput}
                value={rechargeAmount}
                onChangeText={setRechargeAmount}
                placeholder="أدخل المبلغ"
                keyboardType="numeric"
                textAlign="center"
              />
            </View>

            {/* المبالغ السريعة */}
            <View style={styles.quickAmountsSection}>
              <Text style={styles.inputLabel}>مبالغ سريعة</Text>
              <View style={styles.quickAmountsGrid}>
                {quickAmounts.map((amount) => (
                  <TouchableOpacity
                    key={amount}
                    style={[
                      styles.quickAmountBtn,
                      rechargeAmount === amount.toString() && styles.quickAmountBtnSelected
                    ]}
                    onPress={() => setRechargeAmount(amount.toString())}
                  >
                    <Text style={[
                      styles.quickAmountText,
                      rechargeAmount === amount.toString() && styles.quickAmountTextSelected
                    ]}>
                      {amount} جنيه
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* طريقة الدفع */}
            <View style={styles.methodSection}>
              <Text style={styles.inputLabel}>طريقة الدفع</Text>
              <View style={styles.methodsGrid}>
                {rechargeMethods.map((method) => (
                  <TouchableOpacity
                    key={method}
                    style={[
                      styles.methodBtn,
                      selectedMethod === method && styles.methodBtnSelected
                    ]}
                    onPress={() => setSelectedMethod(method)}
                  >
                    <Text style={[
                      styles.methodText,
                      selectedMethod === method && styles.methodTextSelected
                    ]}>
                      {method}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* أزرار التحكم */}
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setRechargeModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>إلغاء</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.confirmButton}
                onPress={handleRechargeSubmit}
              >
                <Text style={styles.confirmButtonText}>شحن المحفظة</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  userInfo: {
    flex: 1,
  },
  welcomeText: {
    fontSize: 16,
    color: '#7f8c8d',
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 5,
  },
  nationalId: {
    fontSize: 14,
    color: '#95a5a6',
    marginTop: 2,
  },
  logoutButton: {
    backgroundColor: '#e74c3c',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 5,
  },
  logoutText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  balanceCard: {
    backgroundColor: '#3498db',
    margin: 20,
    padding: 25,
    borderRadius: 15,
    alignItems: 'center',
  },
  balanceLabel: {
    fontSize: 16,
    color: '#fff',
    opacity: 0.9,
  },
  balanceAmount: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
    marginVertical: 10,
  },
  rechargeButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 15,
    width: '100%',
  },
  rechargeButton: {
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 25,
    flex: 1,
    marginRight: 10,
    alignItems: 'center',
  },
  rechargeButtonText: {
    color: '#3498db',
    fontWeight: 'bold',
    fontSize: 16,
  },
  quickRechargeButton: {
    backgroundColor: '#2ecc71',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 25,
    flex: 1,
    marginLeft: 10,
    alignItems: 'center',
  },
  quickRechargeButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  actionsContainer: {
    margin: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 15,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    backgroundColor: '#fff',
    width: '48%',
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#eee',
  },
  actionText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2c3e50',
  },
  transactionsContainer: {
    margin: 20,
  },
  transactionItem: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#eee',
  },
  transactionInfo: {
    flex: 1,
  },
  transactionType: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
  },
  transactionDate: {
    fontSize: 12,
    color: '#7f8c8d',
    marginTop: 2,
  },
  transactionMethod: {
    fontSize: 12,
    color: '#95a5a6',
    marginTop: 2,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  positiveAmount: {
    color: '#27ae60',
  },
  negativeAmount: {
    color: '#e74c3c',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 20,
    width: '100%',
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 20,
    textAlign: 'center',
  },
  amountSection: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 10,
    textAlign: 'right',
  },
  amountInput: {
    backgroundColor: '#f9f9f9',
    borderRadius: 10,
    padding: 15,
    fontSize: 18,
    borderWidth: 1,
    borderColor: '#ddd',
    textAlign: 'center',
    fontWeight: 'bold',
  },
  quickAmountsSection: {
    marginBottom: 20,
  },
  quickAmountsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickAmountBtn: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 10,
    width: '48%',
    alignItems: 'center',
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  quickAmountBtnSelected: {
    backgroundColor: '#3498db',
    borderColor: '#3498db',
  },
  quickAmountText: {
    fontSize: 14,
    color: '#2c3e50',
  },
  quickAmountTextSelected: {
    color: '#fff',
    fontWeight: 'bold',
  },
  methodSection: {
    marginBottom: 20,
  },
  methodsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  methodBtn: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 10,
    width: '48%',
    alignItems: 'center',
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  methodBtnSelected: {
    backgroundColor: '#3498db',
    borderColor: '#3498db',
  },
  methodText: {
    fontSize: 14,
    color: '#2c3e50',
  },
  methodTextSelected: {
    color: '#fff',
    fontWeight: 'bold',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  cancelButton: {
    backgroundColor: '#e74c3c',
    borderRadius: 10,
    padding: 15,
    flex: 1,
    marginRight: 10,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  confirmButton: {
    backgroundColor: '#2ecc71',
    borderRadius: 10,
    padding: 15,
    flex: 1,
    marginLeft: 10,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

export default WalletScreen;
