/**
 * خدمة المحفظة
 * تحتوي على جميع العمليات المتعلقة بالمحفظة والمعاملات المالية
 */

import { apiGet, apiPost, ApiResponse } from './apiConfig';

// واجهات البيانات
export interface WalletBalance {
  balance: number;
  currency: string;
  lastUpdated: string;
}

export interface Transaction {
  id: string;
  type: 'تجديد' | 'دفع' | 'تحويل';
  amount: number;
  date: string;
  method: string;
  description: string;
  status: 'مكتمل' | 'معلق' | 'فاشل';
  reference?: string;
}

export interface RechargeRequest {
  amount: number;
  method: string;
  paymentDetails?: {
    cardNumber?: string;
    phoneNumber?: string;
    bankAccount?: string;
  };
}

export interface RechargeResponse {
  transactionId: string;
  amount: number;
  newBalance: number;
  status: string;
  paymentReference?: string;
}

export interface TransferRequest {
  recipientNationalId: string;
  amount: number;
  description?: string;
  pin?: string;
}

export interface TransferResponse {
  transactionId: string;
  amount: number;
  recipientName: string;
  newBalance: number;
  status: string;
}

export interface PaymentRequest {
  serviceType: string;
  serviceId: string;
  amount: number;
  description?: string;
}

export interface PaymentResponse {
  transactionId: string;
  amount: number;
  serviceName: string;
  newBalance: number;
  status: string;
  receipt?: string;
}

export interface TransactionHistory {
  transactions: Transaction[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
}

/**
 * الحصول على رصيد المحفظة
 * @returns وعد بالاستجابة
 */
export const getWalletBalance = async (): Promise<ApiResponse<WalletBalance>> => {
  return apiGet<WalletBalance>('/wallet/balance');
};

/**
 * شحن المحفظة
 * @param rechargeData بيانات الشحن
 * @returns وعد بالاستجابة
 */
export const rechargeWallet = async (rechargeData: RechargeRequest): Promise<ApiResponse<RechargeResponse>> => {
  return apiPost<RechargeResponse>('/wallet/recharge', rechargeData);
};

/**
 * تحويل الأموال
 * @param transferData بيانات التحويل
 * @returns وعد بالاستجابة
 */
export const transferMoney = async (transferData: TransferRequest): Promise<ApiResponse<TransferResponse>> => {
  return apiPost<TransferResponse>('/wallet/transfer', transferData);
};

/**
 * دفع فاتورة
 * @param paymentData بيانات الدفع
 * @returns وعد بالاستجابة
 */
export const payBill = async (paymentData: PaymentRequest): Promise<ApiResponse<PaymentResponse>> => {
  return apiPost<PaymentResponse>('/wallet/pay', paymentData);
};

/**
 * الحصول على سجل المعاملات
 * @param page رقم الصفحة (اختياري، افتراضي: 1)
 * @param limit عدد العناصر في الصفحة (اختياري، افتراضي: 20)
 * @param type نوع المعاملة (اختياري)
 * @param startDate تاريخ البداية (اختياري)
 * @param endDate تاريخ النهاية (اختياري)
 * @returns وعد بالاستجابة
 */
export const getTransactionHistory = async (
  page: number = 1,
  limit: number = 20,
  type?: string,
  startDate?: string,
  endDate?: string
): Promise<ApiResponse<TransactionHistory>> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  });

  if (type) params.append('type', type);
  if (startDate) params.append('startDate', startDate);
  if (endDate) params.append('endDate', endDate);

  return apiGet<TransactionHistory>(`/wallet/transactions?${params.toString()}`);
};

/**
 * الحصول على تفاصيل معاملة محددة
 * @param transactionId معرف المعاملة
 * @returns وعد بالاستجابة
 */
export const getTransactionDetails = async (transactionId: string): Promise<ApiResponse<Transaction>> => {
  return apiGet<Transaction>(`/wallet/transactions/${transactionId}`);
};

/**
 * الحصول على طرق الدفع المتاحة
 * @returns وعد بالاستجابة
 */
export const getPaymentMethods = async (): Promise<ApiResponse<{ id: string; name: string; icon: string; enabled: boolean }[]>> => {
  return apiGet<{ id: string; name: string; icon: string; enabled: boolean }[]>('/wallet/payment-methods');
};

/**
 * التحقق من صحة رقم الحساب للتحويل
 * @param nationalId الرقم القومي
 * @returns وعد بالاستجابة
 */
export const validateRecipient = async (nationalId: string): Promise<ApiResponse<{ name: string; phoneNumber: string }>> => {
  return apiPost<{ name: string; phoneNumber: string }>('/wallet/validate-recipient', { nationalId });
};

// نقاط النهاية للـ API
export const WALLET_ENDPOINTS = {
  BALANCE: '/wallet/balance',
  RECHARGE: '/wallet/recharge',
  TRANSFER: '/wallet/transfer',
  PAY: '/wallet/pay',
  TRANSACTIONS: '/wallet/transactions',
  PAYMENT_METHODS: '/wallet/payment-methods',
  VALIDATE_RECIPIENT: '/wallet/validate-recipient',
} as const;
