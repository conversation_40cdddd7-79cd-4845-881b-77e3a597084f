import React, { createContext, useContext, useReducer, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { findUser, getUserTransactions, UserData as DatabaseUser } from '../utils/database';

// واجهات البيانات
export interface User {
  nationalId: string;
  name: string;
  isLoggedIn: boolean;
}

export interface Transaction {
  id: string;
  type: 'تجديد' | 'دفع' | 'تحويل';
  amount: number;
  date: string;
  method: string;
  description?: string;
}

export interface WalletState {
  user: User | null;
  balance: number;
  transactions: Transaction[];
  loading: boolean;
  error: string | null;
}

// أنواع الإجراءات
type WalletAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'LOGIN_SUCCESS'; payload: User }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_BALANCE'; payload: number }
  | { type: 'ADD_TRANSACTION'; payload: Transaction }
  | { type: 'SET_TRANSACTIONS'; payload: Transaction[] }
  | { type: 'LOAD_STORED_DATA'; payload: { user: User; balance: number; transactions: Transaction[] } };

// الحالة الأولية
const initialState: WalletState = {
  user: null,
  balance: 0,
  transactions: [],
  loading: false,
  error: null,
};

// دالة الخفض
const walletReducer = (state: WalletState, action: WalletAction): WalletState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload,
        loading: false,
        error: null,
      };
    
    case 'LOGOUT':
      return {
        ...initialState,
      };
    
    case 'UPDATE_BALANCE':
      return { ...state, balance: action.payload };
    
    case 'ADD_TRANSACTION':
      return {
        ...state,
        transactions: [action.payload, ...state.transactions],
      };
    
    case 'SET_TRANSACTIONS':
      return { ...state, transactions: action.payload };
    
    case 'LOAD_STORED_DATA':
      return {
        ...state,
        user: action.payload.user,
        balance: action.payload.balance,
        transactions: action.payload.transactions,
        loading: false,
      };
    
    default:
      return state;
  }
};

// واجهة السياق
interface WalletContextType {
  state: WalletState;
  login: (nationalId: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  rechargeBalance: (amount: number, method: string) => Promise<void>;
  addTransaction: (transaction: Omit<Transaction, 'id' | 'date'>) => Promise<void>;
}

// إنشاء السياق
const WalletContext = createContext<WalletContextType | undefined>(undefined);

// مفاتيح التخزين
const STORAGE_KEYS = {
  USER: '@wallet_user',
  BALANCE: '@wallet_balance',
  TRANSACTIONS: '@wallet_transactions',
};

// مكون المزود
export const WalletProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(walletReducer, initialState);

  // تحميل البيانات المخزنة عند بدء التطبيق
  useEffect(() => {
    loadStoredData();
  }, []);

  const loadStoredData = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const [userStr, balanceStr, transactionsStr] = await Promise.all([
        AsyncStorage.getItem(STORAGE_KEYS.USER),
        AsyncStorage.getItem(STORAGE_KEYS.BALANCE),
        AsyncStorage.getItem(STORAGE_KEYS.TRANSACTIONS),
      ]);

      if (userStr && balanceStr) {
        const user = JSON.parse(userStr);
        const balance = parseFloat(balanceStr);
        const transactions = transactionsStr ? JSON.parse(transactionsStr) : [];

        dispatch({
          type: 'LOAD_STORED_DATA',
          payload: { user, balance, transactions },
        });
      } else {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    } catch (error) {
      console.error('Error loading stored data:', error);
      dispatch({ type: 'SET_ERROR', payload: 'خطأ في تحميل البيانات' });
    }
  };

  const saveUserData = async (user: User, balance: number, transactions: Transaction[]) => {
    try {
      await Promise.all([
        AsyncStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user)),
        AsyncStorage.setItem(STORAGE_KEYS.BALANCE, balance.toString()),
        AsyncStorage.setItem(STORAGE_KEYS.TRANSACTIONS, JSON.stringify(transactions)),
      ]);
    } catch (error) {
      console.error('Error saving user data:', error);
    }
  };

  const login = async (nationalId: string, password: string): Promise<boolean> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      // محاكاة API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // البحث عن المستخدم في قاعدة البيانات
      const userData = findUser(nationalId, password);
      
      if (userData) {
        const user: User = {
          nationalId: userData.nationalId,
          name: userData.name,
          isLoggedIn: true,
        };
        
        const initialBalance = userData.initialBalance;
        const initialTransactions: Transaction[] = getUserTransactions(nationalId);

        dispatch({ type: 'LOGIN_SUCCESS', payload: user });
        dispatch({ type: 'UPDATE_BALANCE', payload: initialBalance });
        dispatch({ type: 'SET_TRANSACTIONS', payload: initialTransactions });
        
        await saveUserData(user, initialBalance, initialTransactions);
        return true;
      } else {
        dispatch({ type: 'SET_ERROR', payload: 'الرقم القومي أو كلمة المرور غير صحيحة' });
        return false;
      }
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'خطأ في تسجيل الدخول' });
      return false;
    }
  };

  const logout = async (): Promise<void> => {
    try {
      await Promise.all([
        AsyncStorage.removeItem(STORAGE_KEYS.USER),
        AsyncStorage.removeItem(STORAGE_KEYS.BALANCE),
        AsyncStorage.removeItem(STORAGE_KEYS.TRANSACTIONS),
      ]);
      dispatch({ type: 'LOGOUT' });
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  const rechargeBalance = async (amount: number, method: string): Promise<void> => {
    try {
      const newBalance = state.balance + amount;
      const transaction: Transaction = {
        id: Date.now().toString(),
        type: 'تجديد',
        amount,
        date: new Date().toISOString().split('T')[0],
        method,
        description: `تجديد رصيد عبر ${method}`,
      };

      dispatch({ type: 'UPDATE_BALANCE', payload: newBalance });
      dispatch({ type: 'ADD_TRANSACTION', payload: transaction });

      if (state.user) {
        await saveUserData(state.user, newBalance, [transaction, ...state.transactions]);
      }
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'خطأ في تجديد الرصيد' });
    }
  };

  const addTransaction = async (transactionData: Omit<Transaction, 'id' | 'date'>): Promise<void> => {
    try {
      const transaction: Transaction = {
        ...transactionData,
        id: Date.now().toString(),
        date: new Date().toISOString().split('T')[0],
      };

      const newBalance = state.balance + transactionData.amount;
      
      dispatch({ type: 'UPDATE_BALANCE', payload: newBalance });
      dispatch({ type: 'ADD_TRANSACTION', payload: transaction });

      if (state.user) {
        await saveUserData(state.user, newBalance, [transaction, ...state.transactions]);
      }
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'خطأ في إضافة العملية' });
    }
  };

  const value: WalletContextType = {
    state,
    login,
    logout,
    rechargeBalance,
    addTransaction,
  };

  return (
    <WalletContext.Provider value={value}>
      {children}
    </WalletContext.Provider>
  );
};

// دالة مساعدة لاستخدام السياق
export const useWallet = (): WalletContextType => {
  const context = useContext(WalletContext);
  if (!context) {
    throw new Error('useWallet must be used within a WalletProvider');
  }
  return context;
};
