# ميزة شحن المحفظة السريع 💰

## الوصف
تم إضافة ميزة شحن المحفظة السريع التي تتيح للمستخدمين شحن محافظهم بسهولة وسرعة من الشاشة الرئيسية.

## المميزات الجديدة

### 1. زر الشحن السريع 🚀
- زر جديد في شاشة المحفظة الرئيسية باسم "شحن سريع"
- يفتح نافذة منبثقة (Modal) للشحن المباشر
- لا يحتاج للانتقال إلى شاشة منفصلة

### 2. واجهة الشحن السريع
- **إدخال المبلغ**: حقل نص لإدخال المبلغ المطلوب
- **مبالغ سريعة**: أزرار للمبالغ الشائعة (50، 100، 200، 500 جنيه)
- **طرق الدفع**: اختيار من بين:
  - فيزا
  - فودافون كاش
  - أورانج موني
  - اتصالات كاش

### 3. الحفظ التلقائي 💾
- يتم حفظ الرصيد الجديد تلقائياً في AsyncStorage
- يتم إضافة العملية إلى سجل المعاملات
- البيانات محفوظة محلياً ولا تضيع عند إغلاق التطبيق

## كيفية الاستخدام

### الطريقة الأولى: الشحن السريع
1. افتح التطبيق وسجل الدخول
2. في الشاشة الرئيسية، اضغط على زر "شحن سريع" (الأخضر)
3. أدخل المبلغ أو اختر من المبالغ السريعة
4. اختر طريقة الدفع
5. اضغط "شحن المحفظة"
6. أكد العملية

### الطريقة الثانية: الشحن التفصيلي
1. اضغط على زر "تجديد الرصيد" (الأبيض)
2. ستنتقل إلى شاشة الشحن التفصيلية
3. اتبع الخطوات المعتادة

## التحديثات التقنية

### الملفات المحدثة:
- `src/screens/WalletScreen.tsx`: إضافة واجهة الشحن السريع
- `src/context/WalletContext.tsx`: دالة `rechargeBalance` محدثة للحفظ

### المكونات الجديدة:
- Modal للشحن السريع
- أزرار المبالغ السريعة
- أزرار طرق الدفع
- نظام التحقق من صحة البيانات

## مثال على الاستخدام

```typescript
// عند الضغط على زر الشحن السريع
const handleQuickRecharge = () => {
  setRechargeModalVisible(true);
};

// عند تأكيد الشحن
const handleRechargeSubmit = async () => {
  const amount = parseFloat(rechargeAmount);
  await rechargeBalance(amount, selectedMethod);
  // يتم الحفظ تلقائياً في AsyncStorage
};
```

## الفوائد
- ✅ سرعة في الشحن
- ✅ واجهة مستخدم بسيطة
- ✅ حفظ تلقائي للبيانات
- ✅ سجل كامل للعمليات
- ✅ تجربة مستخدم محسنة

## ملاحظات
- جميع العمليات محفوظة محلياً
- يمكن استخدام الميزة مع جميع المستخدمين الخمسة
- البيانات لا تضيع عند إعادة تشغيل التطبيق
