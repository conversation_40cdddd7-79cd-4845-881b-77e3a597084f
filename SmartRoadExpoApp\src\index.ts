/**
 * ملف الفهرس الرئيسي لخدمات API
 * يجمع جميع الخدمات والواجهات في مكان واحد
 */

// تصدير التكوين الأساسي
export * from './apiConfig';

// تصدير خدمة المصادقة
export * from './authService';

// تصدير خدمة المحفظة
export * from './walletService';

// تصدير خدمة السيارات
export * from './carService';

// تصدير خدمة الشكاوى
export * from './complaintService';

// تجميع جميع نقاط النهاية
export const API_ENDPOINTS = {
  // المصادقة
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    ME: '/auth/me',
    CHANGE_PASSWORD: '/auth/change-password',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
    VALIDATE: '/auth/validate',
  },
  
  // المحفظة
  WALLET: {
    BALANCE: '/wallet/balance',
    RECHARGE: '/wallet/recharge',
    TRANSFER: '/wallet/transfer',
    PAY: '/wallet/pay',
    TRANSACTIONS: '/wallet/transactions',
    TRANSACTION_DETAILS: '/wallet/transactions/:transactionId',
    PAYMENT_METHODS: '/wallet/payment-methods',
    VALIDATE_RECIPIENT: '/wallet/validate-recipient',
  },
  
  // السيارات
  CARS: {
    MY_CARS: '/cars/my-cars',
    CAR_DETAILS: '/cars/:carId',
    TRANSFER: '/cars/transfer',
    VALIDATE_OWNER: '/cars/validate-owner',
    REGISTER: '/cars/register',
    UPDATE: '/cars/:carId',
    TRANSFER_HISTORY: '/cars/transfer-history',
    TRANSFER_DETAILS: '/cars/transfers/:transferId',
    SEARCH: '/cars/search',
    TRANSFER_FEES: '/cars/:carId/transfer-fees',
  },
  
  // الشكاوى
  COMPLAINTS: {
    SUBMIT: '/complaints',
    MY_COMPLAINTS: '/complaints/my-complaints',
    COMPLAINT_DETAILS: '/complaints/:complaintId',
    UPDATE: '/complaints/:complaintId',
    CATEGORIES: '/complaints/categories',
    STATS: '/complaints/stats',
    SEARCH: '/complaints/search',
    MESSAGES: '/complaints/:complaintId/messages',
    SEND_MESSAGE: '/complaints/:complaintId/messages',
    CLOSE: '/complaints/:complaintId/close',
    REOPEN: '/complaints/:complaintId/reopen',
  },
} as const;

// تجميع جميع أنواع البيانات
export type {
  // التكوين الأساسي
  ApiResponse,
  
  // المصادقة
  LoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  ChangePasswordRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  
  // المحفظة
  WalletBalance,
  Transaction,
  RechargeRequest,
  RechargeResponse,
  TransferRequest,
  TransferResponse,
  PaymentRequest,
  PaymentResponse,
  TransactionHistory,
  
  // السيارات
  Car,
  CarOwnershipTransfer,
  TransferRequest as CarTransferRequest,
  TransferResponse as CarTransferResponse,
  CarRegistration,
  CarRegistrationResponse,
  TransferHistory as CarTransferHistory,
  
  // الشكاوى
  Complaint,
  ComplaintRequest,
  ComplaintResponse,
  ComplaintUpdate,
  ComplaintCategory,
  ComplaintStats,
  ComplaintHistory,
  ComplaintMessage,
} from './authService';

// دوال مساعدة للتعامل مع الأخطاء
export const handleApiError = (error: any): string => {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  
  if (error.message) {
    return error.message;
  }
  
  return 'حدث خطأ غير معروف';
};

// دالة للتحقق من حالة الاتصال
export const checkNetworkConnection = async (): Promise<boolean> => {
  try {
    const response = await fetch(`${API_BASE_URL}/health`, {
      method: 'GET',
      timeout: 5000,
    });
    return response.ok;
  } catch (error) {
    return false;
  }
};

// دالة لإعادة المحاولة
export const retryApiCall = async <T>(
  apiCall: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: any;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await apiCall();
    } catch (error) {
      lastError = error;
      
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
      }
    }
  }
  
  throw lastError;
};
