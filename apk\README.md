# تطبيق المحفظة الإلكترونية - Smart Road Wallet App

## 📱 محتويات المجلد

هذا المجلد يحتوي على ملفات تطبيق المحفظة الإلكترونية:

- `SmartRoadApp.apk` - ملف التطبيق للتثبيت على أجهزة Android
- `SmartRoadApp.apk.info` - معلومات تفصيلية عن التطبيق
- `INSTALLATION_GUIDE.md` - دليل تثبيت التطبيق واستخدامه
- `QR_DOWNLOAD.txt` - معلومات رمز QR للتحميل السريع
- `README.md` - هذا الملف

## 🚀 كيفية استخدام هذه الملفات

### 1. تثبيت التطبيق

انقل ملف `SmartRoadApp.apk` إلى جهاز Android واتبع التعليمات في `INSTALLATION_GUIDE.md` للتثبيت.

### 2. معلومات التطبيق

اطلع على ملف `SmartRoadApp.apk.info` للحصول على معلومات تفصيلية عن التطبيق ومميزاته.

### 3. التحميل السريع

استخدم المعلومات في `QR_DOWNLOAD.txt` لإنشاء رمز QR للتحميل السريع.

## 🔑 حسابات الاختبار

يمكنك استخدام أي من الحسابات التالية للدخول إلى التطبيق:

| الاسم | الرقم القومي | كلمة المرور |
|-------|-------------|-------------|
| أحمد محمد علي | 29801152301234 | 123456 |
| سارة أحمد حسن | 29505201234567 | sara2024 |
| محمد عبد الله | 29203151987654 | mohamed123 |
| فاطمة محمود | 29712082345678 | fatma456 |
| عمر خالد | 29409301876543 | omar789 |

## 🌟 مميزات التطبيق

- **المحفظة الإلكترونية**: إدارة الرصيد والمعاملات المالية
- **شحن سريع**: شحن المحفظة بطرق دفع متعددة
- **نقل ملكية السيارات**: نقل ملكية السيارات بين المستخدمين
- **نظام الشكاوى**: تقديم ومتابعة الشكاوى
- **واجهة عربية**: تصميم متجاوب باللغة العربية
- **تخزين محلي**: حفظ البيانات محلياً على الجهاز

## 🛠️ بناء ملف APK حقيقي

لبناء ملف APK حقيقي، اتبع الخطوات التالية:

1. **تأكد من توفر المتطلبات**:
   - Node.js (الإصدار 18 أو أحدث)
   - Java JDK (الإصدار 11 أو أحدث)
   - Android SDK مع تعيين `ANDROID_HOME`

2. **انتقل إلى مجلد المشروع**:
   ```bash
   cd SmartRoadApp
   ```

3. **قم بتثبيت التبعيات**:
   ```bash
   npm install --legacy-peer-deps
   ```

4. **قم ببناء ملف APK**:
   ```bash
   # على Windows
   .\build-apk.ps1
   
   # على Linux/macOS
   ./android/app/build-apk.sh
   
   # أو يدوياً
   cd android
   ./gradlew assembleRelease
   ```

5. **ستجد ملف APK في**:
   ```
   android/app/build/outputs/apk/release/app-release.apk
   ```

## 📞 الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل:

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +20 19XXX

## ⚠️ إخلاء المسؤولية

هذا التطبيق مخصص للأغراض التعليمية والتجريبية فقط. لا يجب استخدامه لمعاملات مالية حقيقية أو نقل ملكية سيارات فعلية.

## 📄 حقوق الطبع والنشر

© 2025 Smart Road Inc. جميع الحقوق محفوظة.
