/**
 * خدمة المصادقة
 * تحتوي على جميع العمليات المتعلقة بتسجيل الدخول والخروج والمصادقة
 */

import { apiPost, apiGet, ApiResponse } from './apiConfig';

// واجهات البيانات
export interface LoginRequest {
  nationalId: string;
  password: string;
}

export interface LoginResponse {
  user: {
    id: string;
    nationalId: string;
    name: string;
    email: string;
    phoneNumber: string;
  };
  token: string;
  refreshToken: string;
  expiresIn: number;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  token: string;
  refreshToken: string;
  expiresIn: number;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ForgotPasswordRequest {
  nationalId: string;
  email?: string;
  phoneNumber?: string;
}

export interface ResetPasswordRequest {
  resetToken: string;
  newPassword: string;
  confirmPassword: string;
}

/**
 * تسجيل الدخول
 * @param credentials بيانات تسجيل الدخول
 * @returns وعد بالاستجابة
 */
export const login = async (credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> => {
  return apiPost<LoginResponse>('/auth/login', credentials, {}, false);
};

/**
 * تسجيل الخروج
 * @returns وعد بالاستجابة
 */
export const logout = async (): Promise<ApiResponse<null>> => {
  return apiPost<null>('/auth/logout', {});
};

/**
 * تجديد رمز المصادقة
 * @param refreshData بيانات تجديد الرمز
 * @returns وعد بالاستجابة
 */
export const refreshToken = async (refreshData: RefreshTokenRequest): Promise<ApiResponse<RefreshTokenResponse>> => {
  return apiPost<RefreshTokenResponse>('/auth/refresh', refreshData, {}, false);
};

/**
 * الحصول على معلومات المستخدم الحالي
 * @returns وعد بالاستجابة
 */
export const getCurrentUser = async (): Promise<ApiResponse<LoginResponse['user']>> => {
  return apiGet<LoginResponse['user']>('/auth/me');
};

/**
 * تغيير كلمة المرور
 * @param passwordData بيانات تغيير كلمة المرور
 * @returns وعد بالاستجابة
 */
export const changePassword = async (passwordData: ChangePasswordRequest): Promise<ApiResponse<null>> => {
  return apiPost<null>('/auth/change-password', passwordData);
};

/**
 * نسيان كلمة المرور
 * @param forgotData بيانات استرداد كلمة المرور
 * @returns وعد بالاستجابة
 */
export const forgotPassword = async (forgotData: ForgotPasswordRequest): Promise<ApiResponse<{ message: string }>> => {
  return apiPost<{ message: string }>('/auth/forgot-password', forgotData, {}, false);
};

/**
 * إعادة تعيين كلمة المرور
 * @param resetData بيانات إعادة تعيين كلمة المرور
 * @returns وعد بالاستجابة
 */
export const resetPassword = async (resetData: ResetPasswordRequest): Promise<ApiResponse<null>> => {
  return apiPost<null>('/auth/reset-password', resetData, {}, false);
};

/**
 * التحقق من صحة الرمز المميز
 * @returns وعد بالاستجابة
 */
export const validateToken = async (): Promise<ApiResponse<{ valid: boolean }>> => {
  return apiGet<{ valid: boolean }>('/auth/validate');
};

// نقاط النهاية للـ API
export const AUTH_ENDPOINTS = {
  LOGIN: '/auth/login',
  LOGOUT: '/auth/logout',
  REFRESH: '/auth/refresh',
  ME: '/auth/me',
  CHANGE_PASSWORD: '/auth/change-password',
  FORGOT_PASSWORD: '/auth/forgot-password',
  RESET_PASSWORD: '/auth/reset-password',
  VALIDATE: '/auth/validate',
} as const;
