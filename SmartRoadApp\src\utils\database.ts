// قاعدة البيانات الوهمية للمستخدمين
export interface UserData {
  nationalId: string;
  password: string;
  name: string;
  initialBalance: number;
  phoneNumber: string;
  email: string;
}

export interface UserTransaction {
  id: string;
  type: 'تجديد' | 'دفع' | 'تحويل';
  amount: number;
  date: string;
  method: string;
  description: string;
}

// قاعدة بيانات المستخدمين
export const USERS_DATABASE: UserData[] = [
  {
    nationalId: '29801152301234',
    password: '123456',
    name: 'أحمد محمد علي',
    initialBalance: 1250.50,
    phoneNumber: '01012345678',
    email: '<EMAIL>'
  },
  {
    nationalId: '29505201234567',
    password: 'sara2024',
    name: 'سارة أحمد حسن',
    initialBalance: 850.75,
    phoneNumber: '01098765432',
    email: '<EMAIL>'
  },
  {
    nationalId: '29203151987654',
    password: 'mohamed123',
    name: 'محمد عبد الله',
    initialBalance: 2100.00,
    phoneNumber: '01156789012',
    email: '<EMAIL>'
  },
  {
    nationalId: '29712082345678',
    password: 'fatma456',
    name: 'فاطمة محمود',
    initialBalance: 675.25,
    phoneNumber: '01234567890',
    email: '<EMAIL>'
  },
  {
    nationalId: '29409301876543',
    password: 'omar789',
    name: 'عمر خالد',
    initialBalance: 1800.90,
    phoneNumber: '01087654321',
    email: '<EMAIL>'
  }
];

// العمليات الافتراضية لكل مستخدم
export const DEFAULT_TRANSACTIONS: { [key: string]: UserTransaction[] } = {
  '29801152301234': [
    {
      id: '1',
      type: 'تجديد',
      amount: 500,
      date: '2025-07-10',
      method: 'فيزا',
      description: 'تجديد رصيد عبر بطاقة فيزا'
    },
    {
      id: '2',
      type: 'دفع',
      amount: -50,
      date: '2025-07-09',
      method: 'محفظة',
      description: 'دفع فاتورة كهرباء'
    },
    {
      id: '3',
      type: 'تجديد',
      amount: 300,
      date: '2025-07-08',
      method: 'فودافون كاش',
      description: 'تجديد رصيد عبر فودافون كاش'
    }
  ],
  '29505201234567': [
    {
      id: '4',
      type: 'تجديد',
      amount: 200,
      date: '2025-07-11',
      method: 'أورانج موني',
      description: 'تجديد رصيد عبر أورانج موني'
    },
    {
      id: '5',
      type: 'دفع',
      amount: -25,
      date: '2025-07-10',
      method: 'محفظة',
      description: 'دفع فاتورة مياه'
    }
  ],
  '29203151987654': [
    {
      id: '6',
      type: 'تجديد',
      amount: 1000,
      date: '2025-07-12',
      method: 'تحويل بنكي',
      description: 'تجديد رصيد عبر تحويل بنكي'
    },
    {
      id: '7',
      type: 'تحويل',
      amount: -100,
      date: '2025-07-11',
      method: 'محفظة',
      description: 'تحويل أموال لصديق'
    },
    {
      id: '8',
      type: 'دفع',
      amount: -75,
      date: '2025-07-10',
      method: 'محفظة',
      description: 'دفع فاتورة غاز'
    }
  ],
  '29712082345678': [
    {
      id: '9',
      type: 'تجديد',
      amount: 150,
      date: '2025-07-11',
      method: 'اتصالات كاش',
      description: 'تجديد رصيد عبر اتصالات كاش'
    },
    {
      id: '10',
      type: 'دفع',
      amount: -30,
      date: '2025-07-09',
      method: 'محفظة',
      description: 'دفع اشتراك إنترنت'
    }
  ],
  '29409301876543': [
    {
      id: '11',
      type: 'تجديد',
      amount: 800,
      date: '2025-07-12',
      method: 'فيزا',
      description: 'تجديد رصيد عبر بطاقة فيزا'
    },
    {
      id: '12',
      type: 'دفع',
      amount: -120,
      date: '2025-07-11',
      method: 'محفظة',
      description: 'دفع فاتورة تليفون'
    },
    {
      id: '13',
      type: 'تحويل',
      amount: -200,
      date: '2025-07-10',
      method: 'محفظة',
      description: 'تحويل أموال للعائلة'
    }
  ]
};

// دالة للبحث عن مستخدم
export const findUser = (nationalId: string, password: string): UserData | null => {
  const user = USERS_DATABASE.find(
    u => u.nationalId === nationalId && u.password === password
  );
  return user || null;
};

// دالة للحصول على العمليات الافتراضية للمستخدم
export const getUserTransactions = (nationalId: string): UserTransaction[] => {
  return DEFAULT_TRANSACTIONS[nationalId] || [];
};

// دالة للتحقق من صحة الرقم القومي
export const validateNationalId = (nationalId: string): boolean => {
  // التحقق من أن الرقم القومي 14 رقم
  if (!/^\d{14}$/.test(nationalId)) {
    return false;
  }
  
  // التحقق من أن الرقم موجود في قاعدة البيانات
  return USERS_DATABASE.some(user => user.nationalId === nationalId);
};

// دالة للحصول على معلومات المستخدم بالرقم القومي فقط
export const getUserByNationalId = (nationalId: string): UserData | null => {
  return USERS_DATABASE.find(user => user.nationalId === nationalId) || null;
};

// تصدير قائمة بجميع الأرقام القومية للاختبار
export const getAllNationalIds = (): string[] => {
  return USERS_DATABASE.map(user => user.nationalId);
};

// تصدير قائمة بجميع المستخدمين للعرض (بدون كلمات المرور)
export const getAllUsersForDisplay = () => {
  return USERS_DATABASE.map(user => ({
    nationalId: user.nationalId,
    name: user.name,
    phoneNumber: user.phoneNumber,
    email: user.email,
    initialBalance: user.initialBalance
  }));
};
