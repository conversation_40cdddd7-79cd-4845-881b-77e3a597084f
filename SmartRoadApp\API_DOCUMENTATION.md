# توثيق واجهة برمجة التطبيقات (API) لتطبيق المحفظة الإلكترونية

## نظرة عامة

هذا المستند يوثق واجهة برمجة التطبيقات (API) الخاصة بتطبيق المحفظة الإلكترونية. يوفر API مجموعة من النقاط النهائية (endpoints) للتفاعل مع خدمات المصادقة، المحفظة، السيارات، والشكاوى.

## عنوان الخادم الأساسي

```
https://api.smartroad.com/v1
```

## المصادقة

جميع طلبات API (باستثناء تسجيل الدخول وتجديد الرمز) تتطلب مصادقة. يتم ذلك عن طريق إضافة رأس `Authorization` إلى الطلب بالتنسيق التالي:

```
Authorization: Bearer {token}
```

## تنسيق الاستجابة

جميع استجابات API تتبع التنسيق التالي:

```json
{
  "status": "success" | "error",
  "data": { ... },  // في حالة النجاح
  "message": "...",  // رسالة وصفية
  "errors": { ... },  // في حالة الخطأ
  "timestamp": "2025-07-12T12:34:56Z",
  "requestId": "req_123456789"
}
```

## رموز الحالة HTTP

- `200 OK`: تم تنفيذ الطلب بنجاح
- `201 Created`: تم إنشاء مورد جديد بنجاح
- `400 Bad Request`: طلب غير صالح
- `401 Unauthorized`: المصادقة مطلوبة
- `403 Forbidden`: ليس لديك صلاحية للوصول
- `404 Not Found`: المورد غير موجود
- `422 Unprocessable Entity`: بيانات غير صالحة
- `500 Internal Server Error`: خطأ في الخادم

## خدمات API

### 1. خدمة المصادقة

#### تسجيل الدخول

- **طريقة الطلب**: `POST`
- **المسار**: `/auth/login`
- **الوصف**: تسجيل الدخول باستخدام الرقم القومي وكلمة المرور
- **المصادقة**: غير مطلوبة
- **البيانات المطلوبة**:
  ```json
  {
    "nationalId": "29801152301234",
    "password": "123456"
  }
  ```
- **الاستجابة**:
  ```json
  {
    "status": "success",
    "data": {
      "user": {
        "id": "user_123",
        "nationalId": "29801152301234",
        "name": "أحمد محمد علي",
        "email": "<EMAIL>",
        "phoneNumber": "01012345678"
      },
      "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expiresIn": 3600
    },
    "timestamp": "2025-07-12T12:34:56Z"
  }
  ```

#### تسجيل الخروج

- **طريقة الطلب**: `POST`
- **المسار**: `/auth/logout`
- **الوصف**: تسجيل الخروج وإبطال الرمز الحالي
- **المصادقة**: مطلوبة
- **الاستجابة**:
  ```json
  {
    "status": "success",
    "message": "تم تسجيل الخروج بنجاح",
    "timestamp": "2025-07-12T12:34:56Z"
  }
  ```

### 2. خدمة المحفظة

#### الحصول على رصيد المحفظة

- **طريقة الطلب**: `GET`
- **المسار**: `/wallet/balance`
- **الوصف**: الحصول على رصيد المحفظة الحالي
- **المصادقة**: مطلوبة
- **الاستجابة**:
  ```json
  {
    "status": "success",
    "data": {
      "balance": 1250.50,
      "currency": "EGP",
      "lastUpdated": "2025-07-12T12:34:56Z"
    },
    "timestamp": "2025-07-12T12:34:56Z"
  }
  ```

#### شحن المحفظة

- **طريقة الطلب**: `POST`
- **المسار**: `/wallet/recharge`
- **الوصف**: شحن المحفظة بمبلغ معين
- **المصادقة**: مطلوبة
- **البيانات المطلوبة**:
  ```json
  {
    "amount": 200,
    "method": "فيزا",
    "paymentDetails": {
      "cardNumber": "****************"
    }
  }
  ```
- **الاستجابة**:
  ```json
  {
    "status": "success",
    "data": {
      "transactionId": "txn_123456",
      "amount": 200,
      "newBalance": 1450.50,
      "status": "مكتمل",
      "paymentReference": "ref_123456"
    },
    "timestamp": "2025-07-12T12:34:56Z"
  }
  ```

### 3. خدمة السيارات

#### الحصول على قائمة السيارات

- **طريقة الطلب**: `GET`
- **المسار**: `/cars/my-cars`
- **الوصف**: الحصول على قائمة السيارات المملوكة للمستخدم
- **المصادقة**: مطلوبة
- **الاستجابة**:
  ```json
  {
    "status": "success",
    "data": [
      {
        "id": "car_001",
        "brand": "تويوتا",
        "model": "كامري",
        "year": 2020,
        "plateNumber": "أ ب ج 123",
        "color": "أبيض",
        "ownerNationalId": "29801152301234",
        "ownerName": "أحمد محمد علي",
        "registrationDate": "2023-01-15",
        "status": "نشط"
      },
      // ... المزيد من السيارات
    ],
    "timestamp": "2025-07-12T12:34:56Z"
  }
  ```

#### نقل ملكية سيارة

- **طريقة الطلب**: `POST`
- **المسار**: `/cars/transfer`
- **الوصف**: نقل ملكية سيارة إلى مستخدم آخر
- **المصادقة**: مطلوبة
- **البيانات المطلوبة**:
  ```json
  {
    "carId": "car_001",
    "newOwnerNationalId": "29505201234567",
    "reason": "بيع"
  }
  ```
- **الاستجابة**:
  ```json
  {
    "status": "success",
    "data": {
      "transferId": "transfer_123",
      "carId": "car_001",
      "previousOwner": "أحمد محمد علي",
      "newOwner": "سارة أحمد حسن",
      "transferFee": 150,
      "status": "مكتمل",
      "transferDate": "2025-07-12T12:34:56Z",
      "reference": "ref_transfer_123"
    },
    "timestamp": "2025-07-12T12:34:56Z"
  }
  ```

### 4. خدمة الشكاوى

#### تقديم شكوى جديدة

- **طريقة الطلب**: `POST`
- **المسار**: `/complaints`
- **الوصف**: تقديم شكوى جديدة
- **المصادقة**: مطلوبة
- **البيانات المطلوبة**:
  ```json
  {
    "category": "payment",
    "title": "فشل في شحن المحفظة",
    "description": "قمت بشحن المحفظة بمبلغ 200 جنيه عبر فيزا، تم خصم المبلغ من البطاقة لكن لم يتم إضافته للمحفظة",
    "priority": "عالي",
    "contactMethod": "هاتف"
  }
  ```
- **الاستجابة**:
  ```json
  {
    "status": "success",
    "data": {
      "complaintId": "cmp_123",
      "complaintNumber": "CMP123456789",
      "status": "جديد",
      "estimatedResolutionTime": "24-48 ساعة",
      "message": "تم استلام شكواك بنجاح وسيتم التواصل معك قريباً"
    },
    "timestamp": "2025-07-12T12:34:56Z"
  }
  ```

## أمثلة على استخدام API

### مثال 1: تسجيل الدخول والحصول على رصيد المحفظة

```typescript
import { login, getWalletBalance } from '../api';

// تسجيل الدخول
const loginResponse = await login({
  nationalId: '29801152301234',
  password: '123456'
});

// الحصول على رصيد المحفظة
const balanceResponse = await getWalletBalance();
console.log(`الرصيد الحالي: ${balanceResponse.data.balance} ${balanceResponse.data.currency}`);
```

### مثال 2: شحن المحفظة

```typescript
import { rechargeWallet } from '../api';

// شحن المحفظة
const rechargeResponse = await rechargeWallet({
  amount: 200,
  method: 'فيزا',
  paymentDetails: {
    cardNumber: '****************'
  }
});

console.log(`تم شحن المحفظة بمبلغ ${rechargeResponse.data.amount} جنيه`);
console.log(`الرصيد الجديد: ${rechargeResponse.data.newBalance} جنيه`);
```

### مثال 3: نقل ملكية سيارة

```typescript
import { transferCarOwnership } from '../api';

// نقل ملكية سيارة
const transferResponse = await transferCarOwnership({
  carId: 'car_001',
  newOwnerNationalId: '29505201234567',
  reason: 'بيع'
});

console.log(`تم نقل ملكية السيارة بنجاح`);
console.log(`رسوم النقل: ${transferResponse.data.transferFee} جنيه`);
```

### مثال 4: تقديم شكوى

```typescript
import { submitComplaint } from '../api';

// تقديم شكوى
const complaintResponse = await submitComplaint({
  category: 'payment',
  title: 'فشل في شحن المحفظة',
  description: 'قمت بشحن المحفظة بمبلغ 200 جنيه عبر فيزا، تم خصم المبلغ من البطاقة لكن لم يتم إضافته للمحفظة',
  priority: 'عالي',
  contactMethod: 'هاتف'
});

console.log(`تم تقديم الشكوى بنجاح`);
console.log(`رقم الشكوى: ${complaintResponse.data.complaintNumber}`);
```

## التعامل مع الأخطاء

```typescript
import { login, ApiError, handleApiError } from '../api';

try {
  const response = await login({
    nationalId: '29801152301234',
    password: 'wrong_password'
  });
} catch (error) {
  if (error instanceof ApiError) {
    console.error(`خطأ: ${error.message}`);
    console.error(`رمز الحالة: ${error.status}`);
    
    if (error.errors) {
      console.error('تفاصيل الخطأ:', error.errors);
    }
  } else {
    console.error(`خطأ غير معروف: ${handleApiError(error)}`);
  }
}
```

## ملاحظات هامة

1. جميع الطلبات التي تتطلب مصادقة يجب أن تتضمن رأس `Authorization` مع الرمز المميز.
2. يجب التعامل مع الأخطاء بشكل مناسب في جميع الطلبات.
3. الرمز المميز له مدة صلاحية محددة، ويجب تجديده عند انتهاء الصلاحية.
4. جميع البيانات المرسلة والمستلمة يجب أن تكون بتنسيق JSON.
5. جميع التواريخ والأوقات بتنسيق ISO 8601 (YYYY-MM-DDTHH:MM:SSZ).

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق الدعم الفني:

- البريد الإلكتروني: <EMAIL>
- رقم الهاتف: 19XXX
