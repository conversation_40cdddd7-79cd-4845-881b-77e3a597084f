# تحميل تطبيق المحفظة الإلكترونية - Smart Road App

## معلومات التطبيق

- **اسم التطبيق**: Smart Road Wallet App
- **الإصدار**: 1.0.0
- **حجم الملف**: ~25-30 MB (تقريبي)
- **نظام التشغيل**: Android 5.0 (API 21) أو أحدث
- **تاريخ البناء**: 2025-07-12

## كيفية الحصول على ملف APK

نظراً لأن بناء ملف APK يتطلب بيئة تطوير كاملة، يمكنك الحصول على ملف APK بإحدى الطرق التالية:

### الطريقة 1: بناء ملف APK محلياً

1. **تأكد من توفر المتطلبات**:
   - Node.js (الإصدار 18 أو أحدث)
   - Java JDK (الإصدار 11 أو أحدث)
   - Android SDK مع تعيين `ANDROID_HOME`

2. **قم بتثبيت التبعيات**:
   ```bash
   npm install --legacy-peer-deps
   ```

3. **قم ببناء ملف APK**:
   ```bash
   # على Windows
   .\build-apk.ps1
   
   # على Linux/macOS
   ./android/app/build-apk.sh
   
   # أو يدوياً
   cd android
   ./gradlew assembleRelease
   ```

4. **ستجد ملف APK في**:
   ```
   apk/SmartRoadApp.apk
   ```

### الطريقة 2: استخدام خدمة البناء السحابية

يمكنك استخدام خدمات البناء السحابية مثل:
- **Expo EAS Build**
- **Microsoft App Center**
- **GitHub Actions**
- **Bitrise**

### الطريقة 3: طلب ملف APK جاهز

يمكنك التواصل مع فريق التطوير للحصول على ملف APK جاهز:
- البريد الإلكتروني: <EMAIL>

## تثبيت التطبيق

بعد الحصول على ملف APK:

1. **تمكين المصادر غير المعروفة**:
   - اذهب إلى الإعدادات > الأمان
   - فعّل "المصادر غير المعروفة" أو "تثبيت التطبيقات غير المعروفة"

2. **تثبيت التطبيق**:
   - انقل ملف APK إلى جهاز Android
   - اضغط على ملف APK لبدء التثبيت
   - اتبع التعليمات على الشاشة

## مميزات التطبيق

### 🔐 نظام المصادقة الآمن
- تسجيل الدخول بالرقم القومي وكلمة المرور
- 5 حسابات تجريبية جاهزة للاستخدام

### 💰 إدارة المحفظة الإلكترونية
- عرض رصيد المحفظة الحالي
- شحن سريع للمحفظة
- سجل كامل للمعاملات المالية

### 🚗 نقل ملكية السيارات
- عرض قائمة السيارات المملوكة (3 سيارات لكل مستخدم)
- نقل ملكية آمن بين المستخدمين
- رسوم نقل ملكية (150 جنيه)

### 📝 نظام الشكاوى
- تقديم شكاوى مصنفة حسب النوع
- متابعة حالة الشكوى
- نظام أولويات للشكاوى

### 🎨 واجهة مستخدم متطورة
- تصميم عربي متجاوب
- ألوان متناسقة وجذابة
- تجربة مستخدم سلسة

## الحسابات التجريبية

يمكنك تجربة التطبيق باستخدام الحسابات التالية:

| الاسم | الرقم القومي | كلمة المرور | الرصيد الابتدائي |
|-------|-------------|-------------|-----------------|
| أحمد محمد علي | 29801152301234 | 123456 | 1,250.50 جنيه |
| سارة أحمد حسن | 29505201234567 | sara2024 | 850.75 جنيه |
| محمد عبد الله | 29203151987654 | mohamed123 | 2,100.00 جنيه |
| فاطمة محمود | 29712082345678 | fatma456 | 675.25 جنيه |
| عمر خالد | 29409301876543 | omar789 | 1,800.90 جنيه |

## المتطلبات التقنية

### الحد الأدنى:
- Android 5.0 (API level 21)
- 2 GB RAM
- 100 MB مساحة تخزين فارغة
- اتصال بالإنترنت (للمميزات المستقبلية)

### الموصى به:
- Android 8.0 (API level 26) أو أحدث
- 4 GB RAM أو أكثر
- 200 MB مساحة تخزين فارغة

## الأمان والخصوصية

- جميع البيانات محفوظة محلياً على الجهاز
- لا يتم إرسال بيانات شخصية لخوادم خارجية
- كلمات المرور مشفرة ومحمية
- نظام مصادقة آمن

## الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل:

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: 19XXX
- **ساعات العمل**: الأحد - الخميس، 9:00 ص - 5:00 م

## إخلاء المسؤولية

هذا التطبيق مخصص للأغراض التعليمية والتجريبية فقط. لا يجب استخدامه لمعاملات مالية حقيقية أو نقل ملكية سيارات فعلية.

## حقوق الطبع والنشر

© 2025 Smart Road Inc. جميع الحقوق محفوظة.

---

**ملاحظة**: لبناء ملف APK فعلي، يرجى اتباع التعليمات في ملف `APK_BUILD_GUIDE.md`
