import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';

import LoginScreen from '../screens/LoginScreen';
import WalletScreen from '../screens/WalletScreen';
import RechargeScreen from '../screens/RechargeScreen';
import TransferCarScreen from '../screens/TransferCarScreen';
import ComplaintScreen from '../screens/ComplaintScreen';

export type RootStackParamList = {
  Login: undefined;
  Wallet: undefined;
  Recharge: undefined;
  TransferCar: undefined;
  Complaint: undefined;
};

const Stack = createStackNavigator<RootStackParamList>();

const AppNavigator: React.FC = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="Login"
        screenOptions={{
          headerShown: false,
          gestureEnabled: true,
        }}
      >
        <Stack.Screen
          name="Login"
          component={LoginScreen}
          options={{
            title: 'تسجيل الدخول',
          }}
        />
        <Stack.Screen
          name="Wallet"
          component={WalletScreen}
          options={{
            title: 'المحفظة',
            gestureEnabled: false, // منع الرجوع بالإيماءة من شاشة المحفظة
          }}
        />
        <Stack.Screen
          name="Recharge"
          component={RechargeScreen}
          options={{
            title: 'تجديد الرصيد',
          }}
        />
        <Stack.Screen
          name="TransferCar"
          component={TransferCarScreen}
          options={{
            title: 'نقل ملكية سيارة',
          }}
        />
        <Stack.Screen
          name="Complaint"
          component={ComplaintScreen}
          options={{
            title: 'تقديم شكوى',
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
