/**
 * Smart Road Wallet App
 * Electronic Wallet Application
 *
 * @format
 */

import React from 'react';
import { StatusBar } from 'react-native';
import { WalletProvider } from './src/context/WalletContext';
import AppNavigator from './src/navigation/AppNavigator';

function App(): React.JSX.Element {
  return (
    <WalletProvider>
      <StatusBar barStyle="dark-content" backgroundColor="#fff" />
      <AppNavigator />
    </WalletProvider>
  );
}

export default App;
