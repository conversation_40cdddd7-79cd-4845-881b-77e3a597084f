# PowerShell Script to build APK for SmartRoad App

Write-Host "===== Building APK for SmartRoad App =====" -ForegroundColor Green

# Navigate to the project directory
$projectRoot = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $projectRoot

# Check if Java is installed
try {
    $javaVersion = java -version 2>&1
    Write-Host "Java found: $($javaVersion[0])" -ForegroundColor Green
} catch {
    Write-Host "Error: Java is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Java JDK 11 or higher" -ForegroundColor Yellow
    exit 1
}

# Check if Android SDK is installed
if (-not $env:ANDROID_HOME) {
    Write-Host "Error: ANDROID_HOME environment variable is not set" -ForegroundColor Red
    Write-Host "Please install Android SDK and set ANDROID_HOME" -ForegroundColor Yellow
    exit 1
}

# Check if keystore exists, if not create it
$keystorePath = ".\android\app\smartroad-release-key.keystore"
if (-not (Test-Path $keystorePath)) {
    Write-Host "===== Creating release keystore =====" -ForegroundColor Yellow
    
    $keytoolCmd = "keytool -genkeypair -v -storetype PKCS12 -keystore `"$keystorePath`" -alias smartroad-key -keyalg RSA -keysize 2048 -validity 10000 -storepass smartroad123 -keypass smartroad123 -dname `"CN=Smart Road,OU=Mobile,O=Smart Road Inc,L=Cairo,ST=Cairo,C=EG`""
    
    try {
        Invoke-Expression $keytoolCmd
        Write-Host "===== Keystore created successfully =====" -ForegroundColor Green
    } catch {
        Write-Host "Error creating keystore: $_" -ForegroundColor Red
        exit 1
    }
}

# Check if node_modules exists
if (-not (Test-Path ".\node_modules")) {
    Write-Host "===== Installing dependencies =====" -ForegroundColor Yellow
    try {
        npm install --legacy-peer-deps
        if ($LASTEXITCODE -ne 0) {
            throw "npm install failed"
        }
    } catch {
        Write-Host "Error installing dependencies: $_" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "Dependencies already installed" -ForegroundColor Green
}

# Clean the project
Write-Host "===== Cleaning project =====" -ForegroundColor Yellow
Set-Location ".\android"
try {
    .\gradlew.bat clean
    if ($LASTEXITCODE -ne 0) {
        throw "Gradle clean failed"
    }
} catch {
    Write-Host "Error cleaning project: $_" -ForegroundColor Red
    Set-Location $projectRoot
    exit 1
}

# Build the APK
Write-Host "===== Building release APK =====" -ForegroundColor Yellow
try {
    .\gradlew.bat assembleRelease
    if ($LASTEXITCODE -ne 0) {
        throw "Gradle build failed"
    }
} catch {
    Write-Host "Error building APK: $_" -ForegroundColor Red
    Set-Location $projectRoot
    exit 1
}

# Check if build was successful
$apkPath = ".\app\build\outputs\apk\release\app-release.apk"
if (Test-Path $apkPath) {
    Write-Host "===== APK built successfully =====" -ForegroundColor Green
    
    # Create a directory for the APK in the project root
    Set-Location $projectRoot
    if (-not (Test-Path ".\apk")) {
        New-Item -ItemType Directory -Path ".\apk" | Out-Null
    }
    
    # Copy the APK to the project root
    $sourceApk = ".\android\app\build\outputs\apk\release\app-release.apk"
    $destApk = ".\apk\SmartRoadApp.apk"
    
    Copy-Item $sourceApk $destApk -Force
    
    # Get file size
    $fileSize = (Get-Item $destApk).Length
    $fileSizeMB = [math]::Round($fileSize / 1MB, 2)
    
    Write-Host "===== Build completed successfully =====" -ForegroundColor Green
    Write-Host "APK location: $(Resolve-Path $destApk)" -ForegroundColor Cyan
    Write-Host "APK size: $fileSizeMB MB" -ForegroundColor Cyan
    
    # Generate download information
    $downloadInfo = @"
===== SmartRoad App APK Download Information =====

APK File: SmartRoadApp.apk
Size: $fileSizeMB MB
Location: $(Resolve-Path $destApk)
Build Date: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

Installation Instructions:
1. Enable "Unknown Sources" in Android Settings
2. Transfer the APK file to your Android device
3. Open the APK file on your device to install
4. Follow the installation prompts

App Features:
- Electronic Wallet with balance management
- Quick recharge functionality
- Car ownership transfer system
- Complaint submission system
- Secure login with national ID

For support, contact: <EMAIL>
"@
    
    $downloadInfo | Out-File -FilePath ".\apk\README.txt" -Encoding UTF8
    
    Write-Host "Download information saved to: .\apk\README.txt" -ForegroundColor Cyan
    
} else {
    Write-Host "===== APK build failed =====" -ForegroundColor Red
    Set-Location $projectRoot
    exit 1
}

Set-Location $projectRoot
Write-Host "===== Build process completed =====" -ForegroundColor Green
