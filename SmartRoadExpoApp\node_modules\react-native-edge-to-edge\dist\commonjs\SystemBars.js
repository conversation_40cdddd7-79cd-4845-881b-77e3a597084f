"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.SystemBars = SystemBars;
var _react = require("react");
var _reactNative = require("react-native");
var _NativeEdgeToEdgeModule = _interopRequireDefault(require("./specs/NativeEdgeToEdgeModule"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
function isLightColorScheme() {
  const colorScheme = _reactNative.Appearance?.getColorScheme() ?? "light";
  return colorScheme === "light";
}
function resolveSystemBarStyle(style) {
  switch (style) {
    case "auto":
      return isLightColorScheme() ? "dark" : "light";
    case "inverted":
      return isLightColorScheme() ? "light" : "dark";
    default:
      return style;
  }
}
function toNativeBarStyle(style) {
  return style === "light" || style === "dark" ? `${style}-content` : "default";
}

/**
 * Merges the entries stack.
 */
function mergeEntriesStack(entriesStack) {
  return entriesStack.reduce((prev, cur) => {
    for (const prop in cur) {
      if (cur[prop] != null) {
        // @ts-expect-error
        prev[prop] = cur[prop];
      }
    }
    return prev;
  }, {
    statusBarStyle: undefined,
    navigationBarStyle: undefined,
    statusBarHidden: false,
    navigationBarHidden: false
  });
}
function resolveProps({
  hidden,
  style
}) {
  const compactStyle = typeof style === "string";
  const compactHidden = typeof hidden === "boolean";
  return {
    statusBarStyle: compactStyle ? style : style?.statusBar,
    navigationBarStyle: compactStyle ? style : style?.navigationBar,
    statusBarHidden: compactHidden ? hidden : hidden?.statusBar,
    navigationBarHidden: compactHidden ? hidden : hidden?.navigationBar
  };
}

/**
 * Returns an object to insert in the props stack from the props.
 */
function createStackEntry(props) {
  return resolveProps(props);
}
const entriesStack = [];

// Timer for updating the native module values at the end of the frame.
le