/**
 * خدمة الشكاوى
 * تحتوي على جميع العمليات المتعلقة بتقديم ومتابعة الشكاوى
 */

import { apiGet, apiPost, apiPut, ApiResponse } from './apiConfig';

// واجهات البيانات
export interface Complaint {
  id: string;
  complaintNumber: string;
  userId: string;
  userName: string;
  userNationalId: string;
  category: string;
  title: string;
  description: string;
  priority: 'منخفض' | 'متوسط' | 'عالي' | 'عاجل';
  status: 'جديد' | 'قيد المراجعة' | 'قيد المعالجة' | 'مكتمل' | 'مغلق';
  contactMethod: 'هاتف' | 'بريد إلكتروني' | 'رسائل نصية';
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
  assignedTo?: string;
  resolution?: string;
  attachments?: string[];
}

export interface ComplaintRequest {
  category: string;
  title: string;
  description: string;
  priority: 'منخفض' | 'متوسط' | 'عالي' | 'عاجل';
  contactMethod: 'هاتف' | 'بريد إلكتروني' | 'رسائل نصية';
  attachments?: string[];
}

export interface ComplaintResponse {
  complaintId: string;
  complaintNumber: string;
  status: string;
  estimatedResolutionTime: string;
  message: string;
}

export interface ComplaintUpdate {
  status?: string;
  resolution?: string;
  assignedTo?: string;
  notes?: string;
}

export interface ComplaintCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  averageResolutionTime: string;
}

export interface ComplaintStats {
  totalComplaints: number;
  pendingComplaints: number;
  resolvedComplaints: number;
  averageResolutionTime: string;
  categoryBreakdown: {
    category: string;
    count: number;
    percentage: number;
  }[];
}

export interface ComplaintHistory {
  complaints: Complaint[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
}

export interface ComplaintMessage {
  id: string;
  complaintId: string;
  senderId: string;
  senderName: string;
  senderType: 'user' | 'admin' | 'system';
  message: string;
  timestamp: string;
  attachments?: string[];
}

/**
 * تقديم شكوى جديدة
 * @param complaintData بيانات الشكوى
 * @returns وعد بالاستجابة
 */
export const submitComplaint = async (complaintData: ComplaintRequest): Promise<ApiResponse<ComplaintResponse>> => {
  return apiPost<ComplaintResponse>('/complaints', complaintData);
};

/**
 * الحصول على قائمة الشكاوى الخاصة بالمستخدم
 * @param page رقم الصفحة (اختياري، افتراضي: 1)
 * @param limit عدد العناصر في الصفحة (اختياري، افتراضي: 20)
 * @param status حالة الشكوى (اختياري)
 * @param category فئة الشكوى (اختياري)
 * @returns وعد بالاستجابة
 */
export const getUserComplaints = async (
  page: number = 1,
  limit: number = 20,
  status?: string,
  category?: string
): Promise<ApiResponse<ComplaintHistory>> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  });

  if (status) params.append('status', status);
  if (category) params.append('category', category);

  return apiGet<ComplaintHistory>(`/complaints/my-complaints?${params.toString()}`);
};

/**
 * الحصول على تفاصيل شكوى محددة
 * @param complaintId معرف الشكوى
 * @returns وعد بالاستجابة
 */
export const getComplaintDetails = async (complaintId: string): Promise<ApiResponse<Complaint>> => {
  return apiGet<Complaint>(`/complaints/${complaintId}`);
};

/**
 * تحديث شكوى (للإدارة)
 * @param complaintId معرف الشكوى
 * @param updateData بيانات التحديث
 * @returns وعد بالاستجابة
 */
export const updateComplaint = async (complaintId: string, updateData: ComplaintUpdate): Promise<ApiResponse<Complaint>> => {
  return apiPut<Complaint>(`/complaints/${complaintId}`, updateData);
};

/**
 * الحصول على فئات الشكاوى المتاحة
 * @returns وعد بالاستجابة
 */
export const getComplaintCategories = async (): Promise<ApiResponse<ComplaintCategory[]>> => {
  return apiGet<ComplaintCategory[]>('/complaints/categories');
};

/**
 * الحصول على إحصائيات الشكاوى للمستخدم
 * @returns وعد بالاستجابة
 */
export const getComplaintStats = async (): Promise<ApiResponse<ComplaintStats>> => {
  return apiGet<ComplaintStats>('/complaints/stats');
};

/**
 * البحث في الشكاوى
 * @param query نص البحث
 * @param filters فلاتر البحث (اختياري)
 * @returns وعد بالاستجابة
 */
export const searchComplaints = async (
  query: string,
  filters?: {
    status?: string;
    category?: string;
    priority?: string;
    dateFrom?: string;
    dateTo?: string;
  }
): Promise<ApiResponse<Complaint[]>> => {
  const params = new URLSearchParams({ query });
  
  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value);
      }
    });
  }

  return apiGet<Complaint[]>(`/complaints/search?${params.toString()}`);
};

/**
 * الحصول على رسائل الشكوى
 * @param complaintId معرف الشكوى
 * @returns وعد بالاستجابة
 */
export const getComplaintMessages = async (complaintId: string): Promise<ApiResponse<ComplaintMessage[]>> => {
  return apiGet<ComplaintMessage[]>(`/complaints/${complaintId}/messages`);
};

/**
 * إرسال رسالة في الشكوى
 * @param complaintId معرف الشكوى
 * @param message نص الرسالة
 * @param attachments المرفقات (اختياري)
 * @returns وعد بالاستجابة
 */
export const sendComplaintMessage = async (
  complaintId: string,
  message: string,
  attachments?: string[]
): Promise<ApiResponse<ComplaintMessage>> => {
  return apiPost<ComplaintMessage>(`/complaints/${complaintId}/messages`, {
    message,
    attachments,
  });
};

/**
 * إغلاق شكوى
 * @param complaintId معرف الشكوى
 * @param reason سبب الإغلاق (اختياري)
 * @returns وعد بالاستجابة
 */
export const closeComplaint = async (complaintId: string, reason?: string): Promise<ApiResponse<Complaint>> => {
  return apiPut<Complaint>(`/complaints/${complaintId}/close`, { reason });
};

/**
 * إعادة فتح شكوى
 * @param complaintId معرف الشكوى
 * @param reason سبب إعادة الفتح
 * @returns وعد بالاستجابة
 */
export const reopenComplaint = async (complaintId: string, reason: string): Promise<ApiResponse<Complaint>> => {
  return apiPut<Complaint>(`/complaints/${complaintId}/reopen`, { reason });
};

// نقاط النهاية للـ API
export const COMPLAINT_ENDPOINTS = {
  SUBMIT: '/complaints',
  MY_COMPLAINTS: '/complaints/my-complaints',
  COMPLAINT_DETAILS: '/complaints/:complaintId',
  UPDATE: '/complaints/:complaintId',
  CATEGORIES: '/complaints/categories',
  STATS: '/complaints/stats',
  SEARCH: '/complaints/search',
  MESSAGES: '/complaints/:complaintId/messages',
  SEND_MESSAGE: '/complaints/:complaintId/messages',
  CLOSE: '/complaints/:complaintId/close',
  REOPEN: '/complaints/:complaintId/reopen',
} as const;
