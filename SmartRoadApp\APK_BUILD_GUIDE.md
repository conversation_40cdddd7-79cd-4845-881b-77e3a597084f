# دليل بناء ملف APK لتطبيق المحفظة الإلكترونية

## المتطلبات الأساسية

قبل البدء في بناء ملف APK، تأكد من توفر المتطلبات التالية:

1. **Node.js و npm**: يجب تثبيت Node.js الإصدار 18 أو أحدث
2. **Java Development Kit (JDK)**: الإصدار 11 أو أحدث
3. **Android SDK**: مع تعيين متغير البيئة `ANDROID_HOME`
4. **React Native CLI**: يمكن تثبيته عالمياً باستخدام `npm install -g react-native-cli`

## طرق بناء ملف APK

### الطريقة 1: استخدام سكريبت PowerShell (Windows)

تم توفير سكريبت PowerShell لتسهيل عملية بناء ملف APK على نظام Windows:

1. افتح PowerShell كمسؤول
2. انتقل إلى مجلد المشروع
3. قم بتنفيذ السكريبت:

```powershell
.\build-apk.ps1
```

### الطريقة 2: استخدام سكريبت Bash (Linux/macOS)

تم توفير سكريبت Bash لتسهيل عملية بناء ملف APK على أنظمة Linux و macOS:

1. افتح Terminal
2. انتقل إلى مجلد المشروع
3. قم بتعيين صلاحيات التنفيذ للسكريبت:

```bash
chmod +x ./android/app/build-apk.sh
```

4. قم بتنفيذ السكريبت:

```bash
./android/app/build-apk.sh
```

### الطريقة 3: البناء اليدوي

يمكنك أيضاً بناء ملف APK يدوياً باتباع الخطوات التالية:

1. انتقل إلى مجلد المشروع
2. قم بتثبيت التبعيات:

```bash
npm install --legacy-peer-deps
```

3. انتقل إلى مجلد Android:

```bash
cd android
```

4. قم بتنظيف المشروع:

```bash
./gradlew clean
```

5. قم ببناء ملف APK:

```bash
./gradlew assembleRelease
```

6. ستجد ملف APK في المسار التالي:

```
android/app/build/outputs/apk/release/app-release.apk
```

## مفتاح التوقيع (Signing Key)

تم تكوين المشروع لاستخدام مفتاح توقيع افتراضي للإصدار. في بيئة الإنتاج، يجب إنشاء مفتاح توقيع آمن خاص بك.

### إنشاء مفتاح توقيع جديد

يمكنك إنشاء مفتاح توقيع جديد باستخدام الأمر التالي:

```bash
keytool -genkeypair -v -storetype PKCS12 -keystore smartroad-release-key.keystore -alias smartroad-key -keyalg RSA -keysize 2048 -validity 10000
```

### تكوين مفتاح التوقيع في المشروع

بعد إنشاء مفتاح التوقيع، يمكنك تكوينه في المشروع بإحدى الطريقتين:

#### 1. تعديل ملف build.gradle

قم بتعديل ملف `android/app/build.gradle` وتحديث إعدادات `signingConfigs.release`.

#### 2. استخدام ملف gradle.properties

قم بإنشاء أو تعديل ملف `~/.gradle/gradle.properties` وأضف المتغيرات التالية:

```properties
SMARTROAD_UPLOAD_STORE_FILE=path/to/your/keystore.keystore
SMARTROAD_UPLOAD_KEY_ALIAS=your-key-alias
SMARTROAD_UPLOAD_STORE_PASSWORD=your-store-password
SMARTROAD_UPLOAD_KEY_PASSWORD=your-key-password
```

## تثبيت ملف APK

بعد بناء ملف APK، يمكنك تثبيته على جهاز Android باتباع الخطوات التالية:

1. قم بنقل ملف APK إلى جهاز Android
2. افتح ملف APK على الجهاز
3. اتبع تعليمات التثبيت

ملاحظة: قد تحتاج إلى تمكين "المصادر غير المعروفة" في إعدادات Android.

## استكشاف الأخطاء وإصلاحها

### مشكلة: فشل بناء ملف APK

**الحل المحتمل:**
- تأكد من تثبيت جميع التبعيات باستخدام `npm install --legacy-peer-deps`
- تأكد من تعيين متغير البيئة `ANDROID_HOME` بشكل صحيح
- جرب تنظيف المشروع باستخدام `./gradlew clean`

### مشكلة: خطأ في مفتاح التوقيع

**الحل المحتمل:**
- تأكد من وجود ملف مفتاح التوقيع في المسار الصحيح
- تأكد من صحة كلمة المرور واسم المفتاح

### مشكلة: تعارض في الإصدارات

**الحل المحتمل:**
- تأكد من توافق إصدارات React Native والتبعيات الأخرى
- جرب استخدام `--legacy-peer-deps` عند تثبيت التبعيات

## ملاحظات إضافية

- ملف APK المبني موجود في مجلد `apk` في جذر المشروع
- حجم ملف APK يعتمد على المكتبات المستخدمة والموارد المضمنة
- للحصول على أداء أفضل، يمكنك استخدام ملف AAB بدلاً من APK للنشر على Google Play

## الدعم

إذا واجهت أي مشاكل في بناء ملف APK، يرجى التواصل مع فريق الدعم الفني:

- البريد الإلكتروني: <EMAIL>
